{"namespace": "anti_virus", "entities": [{"entity_name": "anti_ransom", "entity_alias": "anti_ransom", "entity_pk": {"name": "clientid", "ref_fields": ["clientid"]}, "entity_fields": [{"name": "clientid", "type": "string"}, {"name": "assetid", "type": "int64"}, {"name": "assetoid", "type": "int64"}, {"name": "update_time", "type": "int64"}, {"name": "protection_status", "type": "int32"}, {"name": "detection_times", "type": "int64"}, {"name": "threat_events", "type": "int64"}, {"name": "protected_times", "type": "int64"}, {"name": "recovered_files", "type": "int64"}], "relations": [], "asset_fields": ["assetoid", "assetid"]}], "version": "v1.0.0"}