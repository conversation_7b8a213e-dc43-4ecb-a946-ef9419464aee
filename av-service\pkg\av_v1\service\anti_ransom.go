package service 

import (
	"context"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	empty "github.com/golang/protobuf/ptypes/empty"
	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	api "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/av_v1"
)

type AntiRansomService interface {
	
	// 批量读接口
	// 批量读实体client下的资源antiransom,返回keys对应实体下的资源数据集.
	//
	BatchGetClientAntiransom(context.Context, *api.BatchGetClientAntiransom_Request) (*api.BatchGetClientAntiransom_Response, error)

	GetClientAntiransom(context.Context, *api.GetClientAntiransom_Request) (*api.GetClientAntiransom_Response, error)
	// 自定义接口
	// 服务av的自定义实现接口
	PatchAntiransomStatistics(context.Context, *av.PatchAntiRansomStatistics_Request) (*empty.Empty, error)
}

type AntiRansom struct {
	impl    AntiRansomService

}

func NewAntiRansom(s AntiRansomService) *AntiRansom {
	return &AntiRansom{
		impl:    s,
	}
}

func (a *AntiRansom) GetClientAntiransom(ctx context.Context, 
		request *api.GetClientAntiransom_Request) (*api.GetClientAntiransom_Response, error) {
	return a.impl.GetClientAntiransom(ctx, request)
}

func (a *AntiRansom) BatchGetClientAntiransom(ctx context.Context, 
	request *api.BatchGetClientAntiransom_Request) (*api.BatchGetClientAntiransom_Response, error) {
	
	return a.impl.BatchGetClientAntiransom(ctx, request)
}


func (a *AntiRansom) PatchAntiransomStatistics(ctx context.Context, 
	request *av.PatchAntiRansomStatistics_Request) (*empty.Empty, error) {
	
		if request.GetKey() == nil {
			return nil, status.Error(codes.InvalidArgument, "ClientId is nil")
		}

		if request.GetKey().GetId() == "" {
			return nil, status.Error(codes.InvalidArgument, "ClientId.Id is nil")
		}

		if request.GetKey().GetAssetId() == nil {
			return nil, status.Error(codes.InvalidArgument, "ClientId.AssetId is nil")
		}

		if request.GetKey().GetAssetId().GetOid() <= 0 {
			return nil, status.Error(codes.InvalidArgument, "ClientId.AssetId.Oid is nil")
		}

		if request.GetKey().GetAssetId().GetId() <= 0 {
			return nil, status.Error(codes.InvalidArgument, "ClientId.AssetId.Id is nil")
		}

		if request.GetUpdateTime() <= 0 || request.GetProtectionStatus() < 0 || 
			request.GetDetectionTimes() < 0 || request.GetThreatEvents() < 0 || 
			request.GetProtectedTimes() < 0 || request.GetRecoveredFiles() < 0 {
			return nil, status.Error(codes.InvalidArgument, "AntiRansom Statistics is invalid, less than zero ")
		}
	
		return a.impl.PatchAntiransomStatistics(ctx, request)
}