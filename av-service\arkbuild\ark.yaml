---
spec_version: v4
repo: skylar
app_name: av-service
project_name: project.skylar.business.av
build:
  loong64:
    from_ark: "trantor-linux-loong64"
    add_files:
      - "../av-service:/home/<USER>/av-service"
    entrypoint:
      - /home/<USER>/av-service
    build_cmd:
      - 'go build -o av-service ./cmd/server/main.go'
  amd64:
    from_ark: "trantor-linux-x86" 
    add_files:
      - "../av-service:/home/<USER>/av-service"
    entrypoint:
      - /home/<USER>/av-service
    build_cmd:
      - 'go build -o av-service ./cmd/server/main.go'
  arm64:
    from_ark: "trantor-linux-arm64"
    add_files:
      - "../av-service:/home/<USER>/av-service"
    entrypoint:
      - /home/<USER>/av-service
    build_cmd:
      - 'go build -o av-service ./cmd/server/main.go'
  windows:
    build_cmd:
      - 'go build -o av-service.exe ./cmd/server/main.go'
run:
  default:
    cmd: /home/<USER>/av-service
    args:
      - -config=$(CONFIG)
      - -logger=$(LOGGER)
      - -pub-sub=false
      - -config.ns=$(CONFIG_NS)
      
config:
  service:
    - key: server.http.enable
      default: "true"
      desc: "是否启用http"
    - key: server.http.addr
      default: auto
      desc: '服务端http地址; optionals: address, auto, mux'

resources:
  - key: db
    tag: av
  - key: registry
    tag: av
  - key: logger
    tag: av
  - key: config
    tag: av
