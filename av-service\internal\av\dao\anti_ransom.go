package dao 

import (
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
	sorm "git-biz.qianxin-inc.cn/ats-rd2-infra-components/sdk/sorm.git/orm"
	model "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/model"
	"git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/client"
	"github.com/pkg/errors"
	"time"
)

type AntiRansomDao struct {
	Provider server.ServiceProvider
}

func NewAntiRansomDao(sp server.ServiceProvider) *AntiRansomDao {
	d := &AntiRansomDao{
		Provider: sp,
	}
	
	db := sorm.NewOrmWithDb(d.Provider.DefaultDB())
	if err := db.RegisterTable(new(model.AntiRansom)); err != nil {
		gflog.Errorf("RegisterTable AntiRansom is failed: %v ", err)
	}

	return d 
}

func (a *AntiRansomDao) GetDb() *sorm.Orm {
	return sorm.NewOrmWithDb(a.Provider.DefaultDB())
}

func (a *AntiRansomDao) Insert(data *model.AntiRansomDTO) error {
	
	schema := data.ConvertOrm() 
	db := a.GetDb()
	err := db.Insert(schema)
	if err == sorm.ErrUniqueConstraint {
		return model.ErrAlreadyExist
	}

	if err != nil {
		return errors.Wrap(err, "db: add a ransom to table anti_ransom failed") 
	}
	return nil
}

func (a *AntiRansomDao) Update(data *model.AntiRansomDTO) error {
	schema := data.ConvertOrm()
	val := make(map[string]interface{})

	db := a.GetDb()
	qs := db.QueryTable(new(model.AntiRansom)).WithFilter("clientid", schema.ClientId)
	qs = qs.WithFilter("assetid", schema.AssetId).WithFilter("assetoid", schema.AssetOid)

	val["update_time"] = time.Now().Unix()
	val["protection_status"] = schema.ProtectionStatus
	val["detection_times"] = schema.DetectionTimes
	val["threat_events"] = schema.ThreatEvents
	val["protected_times"] = schema.ProtectedTimes
	val["recovered_files"] = schema.RecoveredFiles

	_, err := qs.QsUpdate(val)
	return errors.Wrap(err, "db: update a ransom data to table anti_ransom failed") 
}

func (a *AntiRansomDao) FindOne(clientId *client.ClientId) (*model.AntiRansomDTO, error) {
	data := &model.AntiRansom{}
	
	cond := sorm.NewCondition().And("clientid", clientId.GetId())
	cond = cond.And("assetid", clientId.GetAssetId().GetId()).And("assetoid", clientId.GetAssetId().GetOid())
	qs := a.GetDb().QueryTable(new(model.AntiRansom))
	qs = qs.WithCond(cond)
	err := qs.One(data)
	if err != nil {
		return nil, err 
	}

	return data.ConvertDTO(), nil 
}