package main 

import (
	"context"
	"google.golang.org/grpc/metadata"
	"git-biz.qianxin-inc.cn/infra-components/data-access-framework/golang/data-access-go.git/da"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	"git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/jarvis_v1"
	jcli "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/jarvis_v1/client"
	jarvis "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/jarvis"

)

var jClient jarvis_v1.JarvisV1Client

func main() {
	defer da.Init()()
	jClient, _ = jcli.NewJarvisV1ThinClient()

	var offset, limit int64 = 0, 20
	for {
		gflog.Debugf("GetPolicies ---")
		i, err := GetPolicies(offset, limit)
		if err != nil {
			return 
		}

		if i == 0 {
			break
		}

		offset += 20
	}

}

func GetPolicies(offset, limit int64) (int64, error) {

	policyRequest := &jarvis_v1.GetPoliciesWithExtraFilterPartially_Request{
		Offset: offset,
		Limit: limit,
		//Orders: TODO:看一下这个排序是怎么做的。待调研
		ExtraFilter: &jarvis.PoliciesFilter{
			HasOwner: &jarvis.PoliciesFilter_Owner{
				Owner: &jarvis.PolicyMeta_Owner{
					OwnerType: jarvis.PolicyMeta_Owner_CLIENT,
				},
			},
			HasProject: &jarvis.PoliciesFilter_Project{
				Project: &jarvis.SchemaValue_Project{
					Name: "antivirus",
				},
			},
		},
	}

	newCtx := context.Background()
	newCtx = metadata.AppendToOutgoingContext(newCtx, "x-authorization-advanced", "Bearer Mjg1MjMwNDkwMTUzMzMzNjQ3MjoyODgxNTc2MzQ0NTk5MTM1MTkxOjY3MWQwNGQyLWYxMjgtNDlmZi1iODdhLWM3MjhlNWMzYWVhOQ==")
	newCtx = metadata.AppendToOutgoingContext(newCtx, "authorization", "Bearer Mjg1MjMwNDkwMTUzMzMzNjQ3MjoyODgxNTc2MzQ0NTk5MTM1MTkxOjY3MWQwNGQyLWYxMjgtNDlmZi1iODdhLWM3MjhlNWMzYWVhOQ==")
	policyResp, err := jClient.GetPoliciesWithExtraFilterPartially(newCtx, policyRequest)
	if err != nil {
		gflog.Errorf("GetPoliciesWithExtraFilterPartially failed err=%v", err)
		return -1, err 
	}

	ids := policyResp.GetPolicies() 
	//gflog.Debugf("Offset=%v Get Policy policy=%v", offset, )
	for i := 0; i < len(ids); i++ {
		id := ids[i]
		gflog.Debugf("Offset=%v Get Policy index=%d id=%v ", offset, i, id.Id)
		metaResponse, err := jClient.GetPolicyMeta(newCtx, &jarvis_v1.GetPolicyMeta_Request{
			Key: id,
		})

		if err != nil {
			gflog.Errorf("GetPolicyMeta failed err=%v", err)
			return -1, err
		}
		gflog.Errorf("metaResponse is %v", metaResponse)
		objectResponse, err := jClient.GetPolicyObjects(newCtx, &jarvis_v1.GetPolicyObjects_Request{
			Key: id,
		})

		if err != nil {
			gflog.Errorf("GetPolicyObjects failed err=%v", err)
			return -1, err
		}

		//items := make([]*jarvis.MapObjectKeyObjectValueEntry, len(objectResponse.GetObjects()))
		
		for i := 0; i < len(objectResponse.GetObjects()); i++ {
			obj := objectResponse.GetObjects()[i]
			gflog.Errorf("objectResponse index=%v Object is %v", i, obj)
		}

		
	}

	return int64(len(ids)), nil
}