package av_v1

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAntiVirusServiceV1_Stop(t *testing.T) {
	service := &AntiVirusServiceV1{}

	err := service.Stop()

	// The Stop method should not return an error
	assert.NoError(t, err)
}

func TestServer_Initialization(t *testing.T) {
	// Test that the global Server variable is properly initialized
	assert.NotNil(t, Server)
	assert.IsType(t, &AntiVirusServiceV1{}, Server)
}
