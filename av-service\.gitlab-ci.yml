# 这里是模版引用，不要修改
include:
  - https://git-open.qianxin-inc.cn/devops/auto-devops/-/raw/master/Auto-DevOps.gitlab-ci.yml
 
 
variables:
  # ARK项目名称，需要自动化部署 此参数必填
  ARK_PROJECT_NAME: '天擎'
 
  # ARK环境名称，需要自动化部署 此参数必填， value 值对应的就是要部署的环境。
  ARK_ENV_NAME: '天擎V10安全防护预研'
 
  # ARK部署 需要自动部署的分支名称（可以使用正则表达式匹配多个分支），当前分支如果匹配则自动进行 ark 部署，默认为空
  DEV_DEPLOY_BRANCH_PATTERN: 'feature-10.8.0.2000'
 
  # 自动化测试必填分支名
  AUTO_TEST_BRANCH_PATTERN: 'feature-10.8.0.2000'

  # 是否编译Linux版本，默认为true
  LINUX_BUILD: 'true'
 
  # 是否边缘Windows版本，默认为true
  WINDOWS_BUILD: 'true'
 
  # 是否migration
  IS_MIGRATION: 'false'

  # 增加 arm环境编译
  ARM_BUILD: 'true'

  # 增加 loongarch64 环境编译
  LOONG_BUILD: 'true'
  # 只编译二进制
  # LINUX_LOONG_DARWIN_ARGS: '-is_binary=true'

  # 编译失败后，需要发送邮件的人。
  RECEIVE_EMAIL_LIST: '<EMAIL>,<EMAIL>'

  # 单元测试指定指令
  GO_TEST_COMMAND: "make test"

