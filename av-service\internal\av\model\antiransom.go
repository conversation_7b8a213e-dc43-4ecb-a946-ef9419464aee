package model

import (
	sorm "git-biz.qianxin-inc.cn/ats-rd2-infra-components/sdk/sorm.git/orm"
	"git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/zeus"
	"git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/client"
)

var (
	ErrAlreadyExist error =  sorm.ErrUniqueConstraint
)

//go:generate ergen -types=AntiRansom
type AntiRansom struct {
	ClientId		 string  `orm:"column(clientid);pk"`
	AssetId          int64   `orm:"column(assetid)"`
	AssetOid         int64   `orm:"column(assetoid)"`
	UpdateTime       int64  `orm:"column(update_time);index"`
	ProtectionStatus int32   `orm:"column(protection_status)"`
	DetectionTimes   int64   `orm:"column(detection_times)"`
	ThreatEvents     int64   `orm:"column(threat_events)"`
	ProtectedTimes   int64   `orm:"column(protected_times)"`
	RecoveredFiles    int64   `orm:"column(recovered_files)"`
}

func (a *AntiRansom) TableName() string {
	return "anti_ransom"
}

func (a *AntiRansom) ConvertDTO() *AntiRansomDTO {

	return &AntiRansomDTO {
		ClientId: &client.ClientId{
			Id: a.ClientId,
			AssetId: &zeus.AssetId{
				Id: a.AssetId,
				Oid: a.AssetOid,
			},
		},
		UpdateTime: a.UpdateTime,
		ProtectedTimes: a.ProtectedTimes,
		DetectionTimes: a.DetectionTimes,
		ThreatEvents: a.ThreatEvents,
		ProtectionStatus: a.ProtectionStatus,
		RecoveredFiles: a.RecoveredFiles,
	}
}

type AntiRansomDTO struct {
	ClientId   *client.ClientId
	UpdateTime int64 
	ProtectionStatus int32 
	DetectionTimes int64 
	ThreatEvents int64
	ProtectedTimes int64  
	RecoveredFiles int64 
}

func (a *AntiRansomDTO) ConvertOrm() *AntiRansom {

	r := &AntiRansom{
		ClientId: a.ClientId.GetId(),
		AssetId: a.ClientId.GetAssetId().GetId(),
		AssetOid: a.ClientId.GetAssetId().GetOid(),
		UpdateTime: a.UpdateTime,
		ProtectionStatus: a.ProtectionStatus,
		DetectionTimes: a.DetectionTimes,
		ThreatEvents: a.ThreatEvents,
		ProtectedTimes: a.ProtectedTimes,
		RecoveredFiles: a.RecoveredFiles,
	} 
	return r 
}

type AntiRansomModel interface {
	Insert(data *AntiRansomDTO) error 
	//使用QsUpdate
	Update(data *AntiRansomDTO) error 
	FindOne(clientId *client.ClientId) (*AntiRansomDTO, error) 
}