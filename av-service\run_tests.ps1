#!/usr/bin/env powershell

Write-Host "Running unit tests for av-service..."

# Change to the av-service directory
Set-Location "av-service"

Write-Host "Testing pkg/av_v1/service..."
go test ./pkg/av_v1/service -cover
if ($LASTEXITCODE -ne 0) {
    Write-Host "pkg/av_v1/service tests failed" -ForegroundColor Red
    exit 1
}

Write-Host "Testing pkg/av_v1..."
go test ./pkg/av_v1 -cover
if ($LASTEXITCODE -ne 0) {
    Write-Host "pkg/av_v1 tests failed" -ForegroundColor Red
    exit 1
}

Write-Host "Testing internal/av/model..."
go test ./internal/av/model -cover
if ($LASTEXITCODE -ne 0) {
    Write-Host "internal/av/model tests failed" -ForegroundColor Red
    exit 1
}

Write-Host "Testing internal/av/common..."
go test ./internal/av/common -cover
if ($LASTEXITCODE -ne 0) {
    Write-Host "internal/av/common tests failed" -ForegroundColor Red
    exit 1
}

Write-Host "Testing internal/av/utils..."
go test ./internal/av/utils -cover
if ($LASTEXITCODE -ne 0) {
    Write-Host "internal/av/utils tests failed" -ForegroundColor Red
    exit 1
}

Write-Host "Testing internal/av/dao..."
go test ./internal/av/dao -cover
if ($LASTEXITCODE -ne 0) {
    Write-Host "internal/av/dao tests failed" -ForegroundColor Red
    exit 1
}

Write-Host "Testing internal/av/impl..."
go test ./internal/av/impl -cover
if ($LASTEXITCODE -ne 0) {
    Write-Host "internal/av/impl tests failed" -ForegroundColor Red
    exit 1
}

Write-Host "All tests completed successfully!" -ForegroundColor Green
