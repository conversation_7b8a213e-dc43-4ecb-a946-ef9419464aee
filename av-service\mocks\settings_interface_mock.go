// Code generated by MockGen. DO NOT EDIT.
// Source: ./utils/settings.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	utils "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/utils"
	gomock "github.com/golang/mock/gomock"
)

// MockSettingsUtilsInterface is a mock of SettingsUtilsInterface interface.
type MockSettingsUtilsInterface struct {
	ctrl     *gomock.Controller
	recorder *MockSettingsUtilsInterfaceMockRecorder
}

// MockSettingsUtilsInterfaceMockRecorder is the mock recorder for MockSettingsUtilsInterface.
type MockSettingsUtilsInterfaceMockRecorder struct {
	mock *MockSettingsUtilsInterface
}

// NewMockSettingsUtilsInterface creates a new mock instance.
func NewMockSettingsUtilsInterface(ctrl *gomock.Controller) *MockSettingsUtilsInterface {
	mock := &MockSettingsUtilsInterface{ctrl: ctrl}
	mock.recorder = &MockSettingsUtilsInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSettingsUtilsInterface) EXPECT() *MockSettingsUtilsInterfaceMockRecorder {
	return m.recorder
}

// GetSettings mocks base method.
func (m *MockSettingsUtilsInterface) GetSettings() (*utils.HealthCheckSettings, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSettings")
	ret0, _ := ret[0].(*utils.HealthCheckSettings)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSettings indicates an expected call of GetSettings.
func (mr *MockSettingsUtilsInterfaceMockRecorder) GetSettings() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSettings", reflect.TypeOf((*MockSettingsUtilsInterface)(nil).GetSettings))
}
