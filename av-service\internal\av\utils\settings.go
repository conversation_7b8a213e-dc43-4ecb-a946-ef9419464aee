package utils 

import (
	"errors"
	"context"
	grpcstatus "google.golang.org/grpc/status"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	api "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/settings_v2"
	"git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/settings_v2/client"
	"git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/settings_v2"

)

type HealthCheckSettings struct {
	HiddenSecConfigCheckEnable bool 
	CloudKillCheckEnable bool
	VbVersionCheckEnable bool
	PerformanceRiskCheckEnable bool
}

type SettingsUtilsInterface interface {
	GetSettings() (*HealthCheckSettings, error)
}

type SettingsUtils struct {
	scli api.SettingsV2Client
	LastSettings *HealthCheckSettings
}

func NewSettingsUtils() SettingsUtilsInterface {
	cli, err := client.NewSettingsV2ThinClient()
	if err != nil {
		gflog.Errorf("NewSettingsV2ThinClient failed err=%v", err)
		return nil
	}
	
	return &SettingsUtils{
		scli: cli, 
	}
}

func (s *SettingsUtils) GetSettings() (*HealthCheckSettings, error) {

	assetid, err := AssetId()
	if err != nil {
		return nil, err
	}

	request := &api.GetConfigsKvsWithConfigsFilter_Request{
		ConfigsFilter:	&settings_v2.ConfigsFilter{
			ConfigEntitys:	[]*settings_v2.ConfigEntity{
				{
					Id: "common.security.zion_styx.cloud_search_request",
					AssetId: assetid,
				},
				{
					Id:"common.security.zion_styx.virus_library_version",
					AssetId: assetid,
				},
				{
					Id: "common.security.zion_styx.hidden_danger_policy",
					AssetId: assetid,
				},
				{
					Id: "common.security.zion_styx.performance_risk_policy",
					AssetId: assetid,
				},
			},
		},
	}
	
	resp, err := s.scli.GetConfigsKvsWithConfigsFilter(context.Background(), request)
	if err != nil {
		gflog.Errorf("GetConfigsKvsWithConfigsFilter Failed err=%v", err)
		return s.LastSettings, err
	}

	kvs := resp.GetConfigsKvs() 
	values := []bool{}
	for _, v := range kvs {

		configs := v.GetConfigKv()
		status := v.GetStatus()
		if status.GetCode() != 0 {
			//gflog.Errorf("GetConfigKv Failed request-index=%d err=%v", i, err)
			return s.LastSettings, grpcstatus.ErrorProto(status)
		}

		for _, v := range configs {
			if v.GetKey().GetKey() == "enabled" {
				values = append(values, v.GetValue().GetBoolScalar().GetValue())
			}
		}
	}

	if len(values) < 4 {
		return s.LastSettings, errors.New("GetConfigKv value invalid, lost settings")
	}

	s.LastSettings = &HealthCheckSettings{
		HiddenSecConfigCheckEnable: values[2], 
		CloudKillCheckEnable: values[0],
		VbVersionCheckEnable: values[1], 
		PerformanceRiskCheckEnable: values[3],
	}

	return s.LastSettings, nil 
}
