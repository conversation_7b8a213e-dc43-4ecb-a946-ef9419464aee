package impl 

import (
	"context"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	status1 "google.golang.org/genproto/googleapis/rpc/status"
	empty "github.com/golang/protobuf/ptypes/empty"
	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	api "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/av_v1"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
	model "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/model"
	gen_type "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av/gen_type"
)

type AntiRansomImpl struct {
	ar 	model.AntiRansomModel
}

func NewAntiRansomImpl(sp server.ServiceProvider) *AntiRansomImpl {
	im := &AntiRansomImpl{
		ar: CreateAntiRansomModel(sp),
	}
	return im
}

func (a *AntiRansomImpl) GetClientAntiransom(ctx context.Context, 
		request *api.GetClientAntiransom_Request)  (*api.GetClientAntiransom_Response, error) {
	return nil, status.Error(codes.Unimplemented, "未实现")		
}

func (a *AntiRansomImpl) BatchGetClientAntiransom(ctx context.Context, 
	request *api.BatchGetClientAntiransom_Request) (*api.BatchGetClientAntiransom_Response, error) {
	

	statuses := make([]*status1.Status, len(request.GetKeys())) 
	headers := make([]*gen_type.Metadata, len(request.GetKeys()))
	trailers := make([]*gen_type.Metadata, len(request.GetKeys()))
	ar := make([]*api.GetClientAntiransom_Response, len(request.GetKeys()))

	//1. 拆出请求
	for i := 0; i < len(request.GetKeys()) ; i ++ {
		key := request.Keys[i]
		clientId := key.GetKey()
		
		headers[i] = &gen_type.Metadata{}
		trailers[i] = &gen_type.Metadata{}
		ar[i] = &api.GetClientAntiransom_Response{}

		//2.检查一下参数是否对
		if request.GetKeys() == nil {
			statuses[i] = &status1.Status{Code: int32(codes.InvalidArgument), Message: "ClientId is nil" }
			continue
		}

		if clientId.GetId() == "" {
			statuses[i] = &status1.Status{Code: int32(codes.InvalidArgument), Message: "ClientId.Id is nil" }
			continue
		}

		if clientId.GetAssetId() == nil {
			statuses[i] = &status1.Status{Code: int32(codes.InvalidArgument), Message: "ClientId.AssetId is nil" }
			continue
		}

		if clientId.GetAssetId().GetOid() <= 0 {
			statuses[i] = &status1.Status{Code: int32(codes.InvalidArgument), Message: "ClientId.AssetId.Oid is nil" }
			continue
		}

		if clientId.GetAssetId().GetId() <= 0 {
			statuses[i] = &status1.Status{Code: int32(codes.InvalidArgument), Message: "ClientId.AssetId.Id is nil" }
			continue
		}

		data, err := a.ar.FindOne(clientId)
		if err == model.ErrAlreadyExist {
			statuses[i] = &status1.Status{Code: int32(codes.NotFound), Message: "NotFound"}
			continue
		}
		
		if err != nil {
			statuses[i] = &status1.Status{Code: int32(codes.Internal), Message: err.Error()}
			continue
		}

		statuses[i] = &status1.Status{Code: 0, Message: "OK"}
		//3. 赋值
		ar[i].Antiransom = &av.AntiRansomSatistics{
				UpdateTime: 	  data.UpdateTime,
				ProtectionStatus: data.ProtectionStatus,
				DetectionTimes:   data.DetectionTimes,
				ThreatEvents:     data.ThreatEvents,
				ProtectedTimes:   data.ProtectedTimes,
				RecoveredFiles:   data.RecoveredFiles,
			}
		
	} 

	resp := &api.BatchGetClientAntiransom_Response{
		Headers: headers,
		Trailers: trailers,
		Status: statuses,
		Antiransoms: ar,         
	}

	return resp, nil 
}

func (a *AntiRansomImpl) PatchAntiransomStatistics(ctx context.Context, 
		request *av.PatchAntiRansomStatistics_Request) (*empty.Empty, error) {
	
	dto := &model.AntiRansomDTO{
		ClientId: request.GetKey(),
		UpdateTime: request.GetUpdateTime(),
		ProtectionStatus: request.GetProtectionStatus(),
		DetectionTimes: request.GetDetectionTimes(),
		ThreatEvents: request.GetThreatEvents(),
		ProtectedTimes: request.GetProtectedTimes(),
		RecoveredFiles: request.GetRecoveredFiles(),
	}
	
	err := a.ar.Insert(dto)
	if err != nil {
		if err == model.ErrAlreadyExist { 
			if err := a.ar.Update(dto); err != nil {
				return nil, err 
			}
			return &empty.Empty{}, nil 
		}
		return nil, err
	}

	return &empty.Empty{}, nil 		
}