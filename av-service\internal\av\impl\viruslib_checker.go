package impl 

import (
	"fmt"
	"time"
	"context"
	"strconv"
	"strings"
	"sync/atomic"
	//"encoding/hex"
	"github.com/robfig/cron/v3"
	"google.golang.org/grpc/codes"
	status "google.golang.org/genproto/googleapis/rpc/status"
	"github.com/golang/protobuf/ptypes/timestamp"
	key "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/upgrade/key"
	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	model "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/model"
	//productPb "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/upgrade/product"
	upgradeApi "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/upgrade_v1"
	pcli "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/upgrade_v1/client"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	utils "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/utils"
)

type VirusLibVersionJobChecker struct {
	stopCh 		chan int
	quickCh 	chan int 
	CheckName 	string
	CheckId 	av.HealthCheckItem 
	cJob 		*cron.Cron
	cli         upgradeApi.UpgradeV1Client
	processFlag int32 //为了避免并发的多次检查
	hc          model.HealthCheckModel
}


func NewVirusLibVersionJobChecker(sp server.ServiceProvider, firstCheck bool) JobChecker {
	
	cli, _ := pcli.NewUpgradeV1ThinClient()
	if cli == nil {
		gflog.Errorf("NewUpgradeV1ThinClient failed")
		return nil
	}
	
	hc := CreateHealthCheckModel(int32(av.HealthCheckItem_VIRUSLIB_VERSION), sp)
	if hc == nil {
		gflog.Errorf("CreateHealthCheckModel failed")
		return nil
	}

	checker := &VirusLibVersionJobChecker{
		CheckName: "病毒库陈旧引导提示",
		CheckId: av.HealthCheckItem_VIRUSLIB_VERSION,
		quickCh: make(chan int),
		stopCh: make(chan int),
		processFlag: IDLE_JOB,
		cli: cli,
		hc: hc,
	}

	c := cron.New(cron.WithSeconds())
	c.Start()
	_, err := c.AddFunc("@midnight", checker.check)
    if err != nil {
        gflog.Errorf("add check to cron job failed err=%v", err)
        return nil	
    }
	checker.cJob = c

	go checker.checkLoop()
	//半小时后检查一次
	if firstCheck {
		go func() {
			time.Sleep(1800 * time.Second)
			checker.check()
		}()
	}
	return checker
}

func (c *VirusLibVersionJobChecker) checkLoop() {

	gflog.Debugf("VirusLibVersionJobChecker checkloop start ...")
	defer c.cJob.Stop() //退出之后停掉定时器
	defer atomic.StoreInt32(&c.processFlag, DEAD_JOB)
	//job只要初始化，就立即检查一次。
	//1. 开关重新开启 2. 服务重新启动 3. 服务第一次安装运行
	c.check()
	for {
		select {
		case <-c.stopCh:
			gflog.Infof("VirusLibVersionJobChecker is shut down...")
			return 
		case <-c.quickCh:
			c.check()
		}
	}
}

func (c *VirusLibVersionJobChecker) getVbsInLicense() ([]*key.ProductId, error){
	assetid, _ := utils.AssetId()
	
	request := &upgradeApi.GetProductListPartially_Request{
		Key: &key.ProductId{
			Id: "0",
			AssetId: assetid,
		},
		Offset: 0,
		Limit: 70,
	}

	result := []*key.ProductId{}

	for {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		resp, err := c.cli.GetProductListPartially(ctx, request)
		if err != nil {
			gflog.Errorf("GetProductListPartially failed err=%v", err)
			return nil, err
		}

		if len(resp.List) == 0 {
			break
		}

		for i := 0; i < len(resp.List); i++ {
			
			//debugStr += resp.List[i].Id + "_"
			if resp.List[i].Id == "skylar_win_virus" || 
				resp.List[i].Id == "skylar_winsrv_virus" || 
				resp.List[i].Id == "skylar_mac_virus" {
				result = append(result, resp.List[i])
			}
		}

		//gflog.Errorf("GetProductListPartially offset=%d products=%s", request.Offset, debugStr)
		request.Offset += request.Limit
	}

	return result, nil
}


func (c *VirusLibVersionJobChecker) check() {

	if !atomic.CompareAndSwapInt32(&c.processFlag, IDLE_JOB, PORCESSING_JOB) {
		gflog.Debugf("VirusLibVersionJobChecker is checking ...")
		return
	} 
	defer atomic.StoreInt32(&c.processFlag, IDLE_JOB)

	gflog.Debugf("VirusLibVersionJobChecker check ...")
	

	vbIds, err := c.getVbsInLicense()
	if err != nil {
		gflog.Errorf("getVbsInLicense failed err=%v", err)
		return 
	}

	keys := []*upgradeApi.GetProductVer_Request{}
	for _, v := range vbIds {
		keys = append(keys, &upgradeApi.GetProductVer_Request{
			Key: v,
		})
	}

	//1. 查询病毒库版本
	request := &upgradeApi.BatchGetProductVer_Request{
		Keys: keys,
	}

	resp, err := c.cli.BatchGetProductVer(context.Background(), request)
	if err != nil {
		gflog.Errorf("BatchGetProductVer failed err=%v", err)
		return 
	}

	if len(resp.Vers) != len(request.Keys) || len(resp.Status) != len(request.Keys) {
		gflog.Errorf("BatchGetProductVer get resp param's num invalid, not %d", len(request.Keys))
		return
	}
	gflog.Errorf("[INFO] BatchGetProductVer Get keys=%v versions = %v ", request.Keys, resp.Vers)

	
	vbResult := []*av.VirusLibCheckResult{}
	for i := 0; i < len(resp.Vers); i++ {
		version := resp.Vers[i].Ver.BaseVersion
		if resp.Status[i].GetCode() == 0 {
			days := GetDelayDays(version)
			if days > 0 {
				var vbType av.VirusLibType

				switch request.Keys[i].GetKey().GetId() {
				case "skylar_win_virus":
					vbType = av.VirusLibType_WIN_PC
				case "skylar_winsrv_virus":
					vbType = av.VirusLibType_WIN_SER
				case "skylar_mac_virus":
					vbType = av.VirusLibType_MACOS
				default:
					continue // 或者处理未预料的情况
				}

				vbResult = append(vbResult, &av.VirusLibCheckResult{
					VbType:   av.VirusLibType_name[int32(vbType)],
					Days:     uint32(days),
					Version:  version,
				})
			}
		}
	}

	var result *av.HealthCheckResult
	if len(vbResult) > 0 { //如果当前三种类型的服务端有一个时间比较长的话
		result = &av.HealthCheckResult{
			CheckId:  av.HealthCheckItem_VIRUSLIB_VERSION,
			ItemName: c.CheckName,
			CheckTime: &timestamp.Timestamp{
				Seconds: time.Now().Unix(),
			},
			VirusLibs: vbResult,
		}
	} else {
		gflog.Errorf("check viruslib=%v, is ok ", vbResult)
	}
	
	//更新到数据库
	c.hc.UpdateResult(int32(av.HealthCheckItem_VIRUSLIB_VERSION), result)
}

func (c *VirusLibVersionJobChecker) StopJob() {
	c.stopCh <- 1
}

func (c *VirusLibVersionJobChecker) TriggerACheck() {
	c.quickCh <- 1
}

func (c *VirusLibVersionJobChecker) GetCheckResult() (*av.HealthCheckResult, *status.Status)  {
	//查询数据库
	result, err := c.hc.GetAllResult(int32(av.HealthCheckItem_VIRUSLIB_VERSION))
	if err != nil {
		gflog.Errorf("VirusLibVersionJobChecker GetCheckResult failed err=%v", err)
		return &av.HealthCheckResult{}, &status.Status{Code: int32(codes.FailedPrecondition), Message: err.Error()}
	}
	gflog.Debugf("VirusLibVersionJobChecker GetCheckResult result=%v", result)
	return result, &status.Status{}
}

func (c *VirusLibVersionJobChecker) GetJobStatus() int32 {
	return atomic.LoadInt32(&c.processFlag)
}


// 去掉末尾的点及其后面的部分，并转换为日期
func parseDateString(input string) (time.Time, error) {
	// 去掉末尾的点及其后面的部分
	parts := strings.SplitN(input, ".", 4)
	if len(parts) < 4 {
		return time.Time{}, fmt.Errorf("input string format is incorrect")
	}
 
	// 将字符串转换为整数
	year, err := strconv.Atoi(parts[0])
	if err != nil {
		return time.Time{}, err
	}

	month, err := strconv.Atoi(parts[1])
	if err != nil {
		return time.Time{}, err
	}

	day, err := strconv.Atoi(parts[2])
	if err != nil {
		return time.Time{}, err
	}
 
	// 创建日期对象
	return time.Date(year, time.Month(month), day, 0, 0, 0, 0, time.UTC), nil
}

func GetDelayDays(version string) int {
	
	date, err := parseDateString(version)
	if err != nil {
		gflog.Errorf("Error parsing date:", err)
		return 0
	}
 
	// 获取今天的日期
	today := time.Now().UTC().Truncate(24 * time.Hour)
 
	// 计算相差的天数
	duration := today.Sub(date)
	days := int(duration.Hours() / 24)

	if days >= 30 && days < 60 {
		return 30
 	} else if days >= 60 && days < 90 {
		return 60
	} else if days >= 90 && days < 180 {
		return 90
	} else if days >= 180 && days < 360 {
		return 180 
	} else if days >= 360 {
		return 360
	}

	return 0
}