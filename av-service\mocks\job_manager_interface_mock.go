// Code generated by MockGen. DO NOT EDIT.
// Source: ./internal/av/impl/job_checker.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	gomock "github.com/golang/mock/gomock"
	status "google.golang.org/genproto/googleapis/rpc/status"
)

// MockJobManagerInterface is a mock of JobManagerInterface interface.
type MockJobManagerInterface struct {
	ctrl     *gomock.Controller
	recorder *MockJobManagerInterfaceMockRecorder
}

// MockJobManagerInterfaceMockRecorder is the mock recorder for MockJobManagerInterface.
type MockJobManagerInterfaceMockRecorder struct {
	mock *MockJobManagerInterface
}

// NewMockJobManagerInterface creates a new mock instance.
func NewMockJobManagerInterface(ctrl *gomock.Controller) *MockJobManagerInterface {
	mock := &MockJobManagerInterface{ctrl: ctrl}
	mock.recorder = &MockJobManagerInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockJobManagerInterface) EXPECT() *MockJobManagerInterfaceMockRecorder {
	return m.recorder
}

// GetAllResults mocks base method.
func (m *MockJobManagerInterface) GetAllResults() ([]*av.HealthCheckResult, []*status.Status) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllResults")
	ret0, _ := ret[0].([]*av.HealthCheckResult)
	ret1, _ := ret[1].([]*status.Status)
	return ret0, ret1
}

// GetAllResults indicates an expected call of GetAllResults.
func (mr *MockJobManagerInterfaceMockRecorder) GetAllResults() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllResults", reflect.TypeOf((*MockJobManagerInterface)(nil).GetAllResults))
}

// GetResult mocks base method.
func (m *MockJobManagerInterface) GetResult(checkId av.HealthCheckItem) (*av.HealthCheckResult, *status.Status) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResult", checkId)
	ret0, _ := ret[0].(*av.HealthCheckResult)
	ret1, _ := ret[1].(*status.Status)
	return ret0, ret1
}

// GetResult indicates an expected call of GetResult.
func (mr *MockJobManagerInterfaceMockRecorder) GetResult(checkId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResult", reflect.TypeOf((*MockJobManagerInterface)(nil).GetResult), checkId)
}

// QuickCheck mocks base method.
func (m *MockJobManagerInterface) QuickCheck(checkId av.HealthCheckItem) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "QuickCheck", checkId)
}

// QuickCheck indicates an expected call of QuickCheck.
func (mr *MockJobManagerInterfaceMockRecorder) QuickCheck(checkId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QuickCheck", reflect.TypeOf((*MockJobManagerInterface)(nil).QuickCheck), checkId)
}

// Start mocks base method.
func (m *MockJobManagerInterface) Start() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Start")
}

// Start indicates an expected call of Start.
func (mr *MockJobManagerInterfaceMockRecorder) Start() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockJobManagerInterface)(nil).Start))
}

// StartJob mocks base method.
func (m *MockJobManagerInterface) StartJob(checkId av.HealthCheckItem) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "StartJob", checkId)
}

// StartJob indicates an expected call of StartJob.
func (mr *MockJobManagerInterfaceMockRecorder) StartJob(checkId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartJob", reflect.TypeOf((*MockJobManagerInterface)(nil).StartJob), checkId)
}

// StopJob mocks base method.
func (m *MockJobManagerInterface) StopJob(checkId av.HealthCheckItem) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "StopJob", checkId)
}

// StopJob indicates an expected call of StopJob.
func (mr *MockJobManagerInterfaceMockRecorder) StopJob(checkId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopJob", reflect.TypeOf((*MockJobManagerInterface)(nil).StopJob), checkId)
}

// MockJobChecker is a mock of JobChecker interface.
type MockJobChecker struct {
	ctrl     *gomock.Controller
	recorder *MockJobCheckerMockRecorder
}

// MockJobCheckerMockRecorder is the mock recorder for MockJobChecker.
type MockJobCheckerMockRecorder struct {
	mock *MockJobChecker
}

// NewMockJobChecker creates a new mock instance.
func NewMockJobChecker(ctrl *gomock.Controller) *MockJobChecker {
	mock := &MockJobChecker{ctrl: ctrl}
	mock.recorder = &MockJobCheckerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockJobChecker) EXPECT() *MockJobCheckerMockRecorder {
	return m.recorder
}

// GetCheckResult mocks base method.
func (m *MockJobChecker) GetCheckResult() (*av.HealthCheckResult, *status.Status) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCheckResult")
	ret0, _ := ret[0].(*av.HealthCheckResult)
	ret1, _ := ret[1].(*status.Status)
	return ret0, ret1
}

// GetCheckResult indicates an expected call of GetCheckResult.
func (mr *MockJobCheckerMockRecorder) GetCheckResult() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCheckResult", reflect.TypeOf((*MockJobChecker)(nil).GetCheckResult))
}

// GetJobStatus mocks base method.
func (m *MockJobChecker) GetJobStatus() int32 {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetJobStatus")
	ret0, _ := ret[0].(int32)
	return ret0
}

// GetJobStatus indicates an expected call of GetJobStatus.
func (mr *MockJobCheckerMockRecorder) GetJobStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetJobStatus", reflect.TypeOf((*MockJobChecker)(nil).GetJobStatus))
}

// StopJob mocks base method.
func (m *MockJobChecker) StopJob() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "StopJob")
}

// StopJob indicates an expected call of StopJob.
func (mr *MockJobCheckerMockRecorder) StopJob() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopJob", reflect.TypeOf((*MockJobChecker)(nil).StopJob))
}

// TriggerACheck mocks base method.
func (m *MockJobChecker) TriggerACheck() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TriggerACheck")
}

// TriggerACheck indicates an expected call of TriggerACheck.
func (mr *MockJobCheckerMockRecorder) TriggerACheck() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TriggerACheck", reflect.TypeOf((*MockJobChecker)(nil).TriggerACheck))
}

// checkLoop mocks base method.
func (m *MockJobChecker) checkLoop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "checkLoop")
}

// checkLoop indicates an expected call of checkLoop.
func (mr *MockJobCheckerMockRecorder) checkLoop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "checkLoop", reflect.TypeOf((*MockJobChecker)(nil).checkLoop))
}
