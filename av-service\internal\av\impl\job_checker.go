package impl

import (
	"fmt"
	"sync"
	"google.golang.org/grpc/codes"
	status "google.golang.org/genproto/googleapis/rpc/status"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	utils "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/utils"
)

const (
	IDLE_JOB         int32 = 1
	PORCESSING_JOB   int32 = 2
	DEAD_JOB         int32 = 3
)

type JobManagerInterface interface {
	QuickCheck(checkId av.HealthCheckItem)                                          // 所有的job临时检查一次，重新检测
	Start()                                                                       // 启动manager和初始化所有的job
	StartJob(checkId av.HealthCheckItem)
	StopJob(checkId av.HealthCheckItem)
	GetResult(checkId av.HealthCheckItem) (*av.HealthCheckResult, *status.Status)
	GetAllResults() ([]*av.HealthCheckResult, []*status.Status)
}

type JobChecker interface {
	checkLoop()                           // 检查
	StopJob()
	TriggerACheck()                       // 触发一次检查
	GetCheckResult() (*av.HealthCheckResult, *status.Status)
	GetJobStatus() int32
}

type JobManager struct {
	jobs  sync.Map
	once  sync.Once
	sp    server.ServiceProvider
	sutils utils.SettingsUtilsInterface
}

func NewJobManager(sp server.ServiceProvider) JobManagerInterface {
	// 创建JobCheckers
	jm := &JobManager{
		sp:    sp,
		sutils: utils.NewSettingsUtils(),
	}
	jm.Start()
	return jm 
}

func (m *JobManager) Start() {
	m.once.Do(func() {
		// 1. 获取setttings
		settings, err := m.sutils.GetSettings()
		if err != nil || settings == nil {
			gflog.Errorf("JobManager start GetSettings failed err=%v ,start all checkers", err)
			m.jobs.Store(av.HealthCheckItem_SEC_HIDDEN_DANGER, NewSecStrategyJobChecker(m.sp, true))
			m.jobs.Store(av.HealthCheckItem_ACCESS_ClOUD, NewCloudKillAccessableJobChecker(m.sp, true))
			m.jobs.Store(av.HealthCheckItem_VIRUSLIB_VERSION, NewVirusLibVersionJobChecker(m.sp, true))
			m.jobs.Store(av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER, NewPerformanceStrategyJobChecker(m.sp, true))
			return
		}

		if settings.HiddenSecConfigCheckEnable {
			m.jobs.Store(av.HealthCheckItem_ACCESS_ClOUD, NewSecStrategyJobChecker(m.sp, true))
		}

		if settings.CloudKillCheckEnable {
			m.jobs.Store(av.HealthCheckItem_ACCESS_ClOUD, NewCloudKillAccessableJobChecker(m.sp, true))
		}

		if settings.VbVersionCheckEnable {
			m.jobs.Store(av.HealthCheckItem_VIRUSLIB_VERSION, NewVirusLibVersionJobChecker(m.sp, true))
		}

		if settings.PerformanceRiskCheckEnable {
			m.jobs.Store(av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER, NewPerformanceStrategyJobChecker(m.sp, true))
		}
	})
}

// 让一个任务开启
func (m *JobManager) StartJob(checkId av.HealthCheckItem) {

	job, ok := m.GetJob(checkId)
	if ok {
		if job.GetJobStatus() != DEAD_JOB {
			// job一切正常，直接退出就可以
			gflog.Debugf("StartJob checkID=%d is already running ...", checkId)
			return
		}
	}

	switch checkId {
	case av.HealthCheckItem_SEC_HIDDEN_DANGER:
		m.jobs.Store(checkId, NewSecStrategyJobChecker(m.sp, false))
	case av.HealthCheckItem_ACCESS_ClOUD:
		m.jobs.Store(checkId, NewCloudKillAccessableJobChecker(m.sp, false))
	case av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER:
		m.jobs.Store(checkId, NewPerformanceStrategyJobChecker(m.sp, false))
	case av.HealthCheckItem_VIRUSLIB_VERSION:
		m.jobs.Store(checkId, NewVirusLibVersionJobChecker(m.sp, false))
	default:
	}
}

func (m *JobManager) StopJob(checkId av.HealthCheckItem) {

	job, ok := m.GetJob(checkId)
	if !ok {
		gflog.Debugf("StopJob checkID=%d is not running ...", checkId)
		return
	}

	if job.GetJobStatus() != DEAD_JOB {
		job.StopJob()
	}

	m.jobs.Delete(checkId)
}

func (m *JobManager) GetJob(checkId av.HealthCheckItem) (JobChecker, bool) {
	job, ok := m.jobs.Load(checkId)
	if !ok {
		return nil, false
	}
	return job.(JobChecker), ok
}

func (m *JobManager) GetResult(checkId av.HealthCheckItem) (*av.HealthCheckResult, *status.Status) {
	// 检查settings，查不到不给
	settings, err := m.sutils.GetSettings()
	if err != nil || settings == nil {
		str := fmt.Sprintf("JobManager GetResult checkId=%v failed err=%v and last settings is nil, return empty result", checkId, err)
		gflog.Errorf("%s", str)
		errorStat := &status.Status{Code: int32(codes.FailedPrecondition), Message: str}
		return &av.HealthCheckResult{}, errorStat
	}

	job, ok := m.GetJob(checkId)
	if CheckCheckerEnable(checkId, settings) {
		if ok {
			return job.GetCheckResult()
		}
		// job没有开启，现开启。结果是空的
		m.StartJob(checkId)
	}

	// 开关关闭
	if ok && job.GetJobStatus() != DEAD_JOB {
		m.StopJob(checkId)
	}

	return &av.HealthCheckResult{}, &status.Status{Code: int32(codes.FailedPrecondition)}
}

func (m *JobManager) GetAllResults() ([]*av.HealthCheckResult, []*status.Status) {

	checkids := []av.HealthCheckItem{
		av.HealthCheckItem_ACCESS_ClOUD,
		av.HealthCheckItem_VIRUSLIB_VERSION,
		av.HealthCheckItem_SEC_HIDDEN_DANGER,
		av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER,
	}

	// 检查settings，查不到不给
	settings, err := m.sutils.GetSettings()
	if err != nil || settings == nil {
		str := fmt.Sprintf("JobManager GetAllResults failed err=%v and last settings is nil, return empty result", err)
		gflog.Errorf("%s", str)
		errorStat := &status.Status{Code: int32(codes.Internal), Message: str}
		errorStats := []*status.Status{errorStat, errorStat, errorStat, errorStat}
		return make([]*av.HealthCheckResult, 4), errorStats
	}

	results := []*av.HealthCheckResult{}
	stats := []*status.Status{}
	for _, checkId := range checkids {
		job, ok := m.GetJob(checkId)
		if CheckCheckerEnable(checkId, settings) {
			
			if ok {
				result, stat := job.GetCheckResult()
				results = append(results, result)
				stats = append(stats, stat)
			} else {
				// job没有开启，开启。结果是空的
				m.StartJob(checkId)
				results = append(results, &av.HealthCheckResult{})
				stats = append(stats, &status.Status{})
			}

		} else {
			
			// 开关关闭
			if ok && job.GetJobStatus() != DEAD_JOB {
				m.StopJob(checkId)
			}
			results = append(results, &av.HealthCheckResult{})
			stats = append(stats, &status.Status{Code: int32(codes.FailedPrecondition)})
		}
	}

	return results, stats
}

func (m *JobManager) QuickCheck(checkId av.HealthCheckItem) {
	settings, err := m.sutils.GetSettings()

	// 这种情况是settings保存之后
	if checkId == av.HealthCheckItem_ALL_ITEMS {
		if err != nil || settings == nil { // 获取settings失败之后
			gflog.Errorf("JobManager start GetSettings failed err=%v, start all checkers", err)
			
			m.jobs.Range(func(id interface{}, job interface{}) bool {
				jc := job.(JobChecker)
				if jc.GetJobStatus() == IDLE_JOB {
					jc.TriggerACheck()
				}
				return true
			})
			return
		}

		checkids := []av.HealthCheckItem{
			av.HealthCheckItem_ACCESS_ClOUD,
			av.HealthCheckItem_VIRUSLIB_VERSION,
			av.HealthCheckItem_SEC_HIDDEN_DANGER,
			av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER,
		}

		for _, checkId := range checkids {
			if CheckCheckerEnable(checkId, settings) {
				job, ok := m.GetJob(checkId)
				if !ok || job.GetJobStatus() == DEAD_JOB {
					// 重新创建job，重新检查
					switch checkId {
					case av.HealthCheckItem_SEC_HIDDEN_DANGER:
						m.jobs.Store(checkId, NewSecStrategyJobChecker(m.sp, false))
					case av.HealthCheckItem_ACCESS_ClOUD:
						m.jobs.Store(checkId, NewCloudKillAccessableJobChecker(m.sp, false))
					case av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER:
						m.jobs.Store(checkId, NewPerformanceStrategyJobChecker(m.sp, false))
					case av.HealthCheckItem_VIRUSLIB_VERSION:
						m.jobs.Store(checkId, NewVirusLibVersionJobChecker(m.sp, false))
					default:
					}
					continue //初始化的时候已经做了一次检查跳过即可
				}

				if job.GetJobStatus() == IDLE_JOB {
					job.TriggerACheck()
				}
			} else {
				m.StopJob(checkId)
			}
		}

		return
	}

	//重新检测
	if err != nil || settings == nil { //获取settings失败之后,啥也不干
		gflog.Errorf("JobManager start GetSettings failed err=%v, start all checkers", err)
		return
	}
	//settings打开了
	if CheckCheckerEnable(checkId, settings) {
		jc, ok := m.GetJob(checkId)
		if ok {
			if jc.GetJobStatus() == IDLE_JOB {
				jc.TriggerACheck()
			}
		}
	}

}

func CheckCheckerEnable(checkId av.HealthCheckItem, settings *utils.HealthCheckSettings) bool {
	if settings.HiddenSecConfigCheckEnable && checkId == av.HealthCheckItem_SEC_HIDDEN_DANGER {
		return true
	}

	if settings.CloudKillCheckEnable && checkId == av.HealthCheckItem_ACCESS_ClOUD  {
		return true
	}

	if settings.VbVersionCheckEnable && checkId == av.HealthCheckItem_VIRUSLIB_VERSION {
		return true
	}

	if settings.PerformanceRiskCheckEnable && checkId == av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER {
		return true
	}

	return false
}