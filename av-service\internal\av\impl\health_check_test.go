package impl

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	status "google.golang.org/genproto/googleapis/rpc/status"
	"google.golang.org/grpc/codes"

	// "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	// "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
	jarvis "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/jarvis"
	"git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	mocks "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/mocks"
)

type HealthCheckTestSuite struct {
	suite.Suite
	ctrl           *gomock.Controller
	hci            *HealthCheckImpl
	mockJMI        *mocks.MockJobManagerInterface
	mockPU         *mocks.MockPolicyUtilsInterface
	ctx            context.Context
	checkRequestId av.HealthCheckItem
}

func (suite *HealthCheckTestSuite) SetupTest() {
	suite.ctrl = gomock.NewController(suite.T())
	suite.mockJMI = mocks.NewMockJobManagerInterface(suite.ctrl)
	suite.mockPU = mocks.NewMockPolicyUtilsInterface(suite.ctrl)
	suite.ctx = context.Background()

	// 设置一个有效的 HealthCheckItem 常量作为示例
	suite.checkRequestId = av.HealthCheckItem_ACCESS_ClOUD
	suite.hci = &HealthCheckImpl{jm: suite.mockJMI, pUtils: suite.mockPU}
}

func (suite *HealthCheckTestSuite) TearDownTest() {
	suite.ctrl.Finish()
}

// 描述：此测试验证健康检查的结果在所有依赖项正常工作的情况下返回正确的结果。
func (suite *HealthCheckTestSuite) TestGetAntivirusHealthCheckResult_Success() {
	// 创建一个新的 PolicyCheckResult 实例
	expectedPolicy := &av.PolicyCheckResult{
		Key: &jarvis.PolicyId{
			Id: 1,
		},
		PolicyName: "ExamplePolicy",
		Objects: []*jarvis.MapObjectKeyObjectValueEntry{
			{
				Key: &jarvis.ObjectKey{
					Project: &jarvis.SchemaValue_Project{
						Name: "antivirus",
					},
					ItemName: "example_key_1",
				},
				Value: &jarvis.ObjectValue{
					Value: &jarvis.ObjectValue_StringScalar{
						StringScalar: &jarvis.String_Scalar{
							Value: "example_value_1",
						},
					},
				},
			},
			{
				Key: &jarvis.ObjectKey{
					Project: &jarvis.SchemaValue_Project{
						Name: "antivirus",
					},
					ItemName: "example_key_2",
				},
				Value: &jarvis.ObjectValue{
					Value: &jarvis.ObjectValue_StringScalar{
						StringScalar: &jarvis.String_Scalar{
							Value: "example_value_2",
						},
					},
				},
			},
		},
		IsCascade: true,
	}

	// 使用新的 PolicyCheckResult 实例构建 expectedResult
	expectedResult := &av.HealthCheckResult{
		Policies: []*av.PolicyCheckResult{expectedPolicy},
	}

	// 设置 mock 方法的期望行为
	suite.mockPU.EXPECT().
		GetAntiVirusPoliciesPartiallyWithStaffid(suite.ctx, int64(20)).
		Return([]*av.PolicyCheckResult{expectedPolicy}, nil)

	suite.mockJMI.EXPECT().
		GetResult(suite.checkRequestId).
		Return(expectedResult, &status.Status{Code: 0})

		// 准备请求参数
	req := &av.GetAntiVirusHealthCheckResult_Request{
		Keys: []*av.AntiVirusHealthCheckResult_Request{
			{
				CheckId: suite.checkRequestId,
				Offset:  0,
				Limit:   20,
			},
		},
	}

	// 执行方法并进行断言
	resp, err := suite.hci.GetAntivirusHealthCheckResult(suite.ctx, req)
	suite.NoError(err)
	suite.Len(resp.Responses, 1)
	suite.Equal(expectedResult, resp.Responses[0].Result)
}

// 描述：此测试验证快速启动抗病毒健康检查后返回正确。
func (suite *HealthCheckTestSuite) TestStartAntivirusHealthCheck_Success() {
	suite.mockJMI.EXPECT().QuickCheck(suite.checkRequestId)

	req := &av.StartAntiVirusHealthCheck_Request{CheckId: suite.checkRequestId}
	resp, err := suite.hci.StartAntivirusHealthCheck(suite.ctx, req)
	suite.NoError(err)
	suite.NotNil(resp)
}

// 描述：此测试验证当 GetAntiVirusPoliciesPartiallyWithStaffid 返回错误时，接口会返回同样的错误信息。
func (suite *HealthCheckTestSuite) TestGetAntivirusHealthCheckResult_GetAntiVirusPoliciesPartiallyWithStaffid_Error() {
	expectedErr := errors.New("模拟的策略获取错误")
	suite.mockPU.EXPECT().GetAntiVirusPoliciesPartiallyWithStaffid(suite.ctx, int64(20)).Return(nil, expectedErr)

	req := &av.GetAntiVirusHealthCheckResult_Request{
		Keys: []*av.AntiVirusHealthCheckResult_Request{
			{
				CheckId: suite.checkRequestId,
				Offset:  0,
				Limit:   20,
			},
		},
	}
	_, err := suite.hci.GetAntivirusHealthCheckResult(suite.ctx, req)
	suite.Error(err)
	suite.Contains(err.Error(), expectedErr.Error())
}

// 描述：此测试验证当 GetAntiVirusPoliciesPartiallyWithStaffid 返回的 policy 切片为空但没有错误时，
// 接口正常返回，但结果是空切片，并且状态码为 codes.FailedPrecondition。
func (suite *HealthCheckTestSuite) TestGetAntivirusHealthCheckResult_EmptyPolicySlice() {
	// Mock GetAntiVirusPoliciesPartiallyWithStaffid 返回空的 Policy 列表
	suite.mockPU.EXPECT().GetAntiVirusPoliciesPartiallyWithStaffid(suite.ctx, int64(20)).Return([]*av.PolicyCheckResult{}, nil)

	// Mock GetResult 返回带有数据的 HealthCheckResult
	suite.mockJMI.EXPECT().GetResult(gomock.Any()).Return(
		&av.HealthCheckResult{
			Policies: []*av.PolicyCheckResult{
				{
					Key:        &jarvis.PolicyId{Id: 1},
					PolicyName: "EmptyPolicy1",
					Objects: []*jarvis.MapObjectKeyObjectValueEntry{
						{
							Key: &jarvis.ObjectKey{
								Project:  &jarvis.SchemaValue_Project{Name: "antivirus"},
								ItemName: "empty_key_1",
							},
							Value: &jarvis.ObjectValue{
								Value: &jarvis.ObjectValue_StringScalar{
									StringScalar: &jarvis.String_Scalar{Value: "empty_value_1"},
								},
							},
						},
					},
					IsCascade: false,
				},
				{
					Key:        &jarvis.PolicyId{Id: 2},
					PolicyName: "EmptyPolicy2",
					Objects: []*jarvis.MapObjectKeyObjectValueEntry{
						{
							Key: &jarvis.ObjectKey{
								Project:  &jarvis.SchemaValue_Project{Name: "antivirus"},
								ItemName: "empty_key_2",
							},
							Value: &jarvis.ObjectValue{
								Value: &jarvis.ObjectValue_StringScalar{
									StringScalar: &jarvis.String_Scalar{Value: "empty_value_2"},
								},
							},
						},
					},
					IsCascade: false,
				},
			},
		},
		&status.Status{},
	)

	// 构建请求并执行
	req := &av.GetAntiVirusHealthCheckResult_Request{
		Keys: []*av.AntiVirusHealthCheckResult_Request{
			{
				CheckId: suite.checkRequestId,
				Offset:  0,
				Limit:   20,
			},
		},
	}
	resp, err := suite.hci.GetAntivirusHealthCheckResult(suite.ctx, req)

	// 断言结果
	suite.NoError(err)
	suite.Len(resp.Responses, 1)
	suite.Empty(resp.Responses[0].Result.Policies)
	suite.Equal(int32(codes.FailedPrecondition), resp.Responses[0].Status.Code)
}

// 描述：此测试验证当 GetAntiVirusPoliciesPartiallyWithStaffid 返回的 policy 切片为空但没有错误时，
// GetResult同时返回一些result，但是code不是0
// 接口正常返回，但结果是空切片，并且状态码为 codes.FailedPrecondition。
func (suite *HealthCheckTestSuite) TestGetAntivirusHealthCheckResult_GetResult_Error() {
	// Mock GetAntiVirusPoliciesPartiallyWithStaffid 返回空的 Policy 列表
	suite.mockPU.EXPECT().GetAntiVirusPoliciesPartiallyWithStaffid(suite.ctx, int64(20)).Return([]*av.PolicyCheckResult{}, nil)

	// Mock GetResult 返回带有数据的 HealthCheckResult
	suite.mockJMI.EXPECT().GetResult(gomock.Any()).Return(
		&av.HealthCheckResult{
			Policies: []*av.PolicyCheckResult{
				{
					Key:        &jarvis.PolicyId{Id: 1},
					PolicyName: "EmptyPolicy1",
					Objects: []*jarvis.MapObjectKeyObjectValueEntry{
						{
							Key: &jarvis.ObjectKey{
								Project:  &jarvis.SchemaValue_Project{Name: "antivirus"},
								ItemName: "empty_key_1",
							},
							Value: &jarvis.ObjectValue{
								Value: &jarvis.ObjectValue_StringScalar{
									StringScalar: &jarvis.String_Scalar{Value: "empty_value_1"},
								},
							},
						},
					},
					IsCascade: false,
				},
				{
					Key:        &jarvis.PolicyId{Id: 2},
					PolicyName: "EmptyPolicy2",
					Objects: []*jarvis.MapObjectKeyObjectValueEntry{
						{
							Key: &jarvis.ObjectKey{
								Project:  &jarvis.SchemaValue_Project{Name: "antivirus"},
								ItemName: "empty_key_2",
							},
							Value: &jarvis.ObjectValue{
								Value: &jarvis.ObjectValue_StringScalar{
									StringScalar: &jarvis.String_Scalar{Value: "empty_value_2"},
								},
							},
						},
					},
					IsCascade: false,
				},
			},
		},
		&status.Status{Code: int32(codes.Internal)},
	)

	// 构建请求并执行
	req := &av.GetAntiVirusHealthCheckResult_Request{
		Keys: []*av.AntiVirusHealthCheckResult_Request{
			{
				CheckId: suite.checkRequestId,
				Offset:  0,
				Limit:   20,
			},
		},
	}
	resp, err := suite.hci.GetAntivirusHealthCheckResult(suite.ctx, req)

	// 断言结果
	suite.NoError(err)
	suite.Len(resp.Responses, 1)
	//suite.Empty(resp.Responses[0].Result.Policies)
	suite.Equal(int32(codes.Internal), resp.Responses[0].Status.Code)
}

// 描述：此测试验证当 GetAntiVirusPoliciesPartiallyWithStaffid 返回的 policy 切片有5个元素，
// 而 hc.jm.GetResult(key.GetCheckId()) 返回的 result 中 Policies 包含10个元素（包括上述5个）时，
// 接口正常返回，但是返回的 result 中 Policies 只有前者的5个值，并且状态码为 codes.OK。
func (suite *HealthCheckTestSuite) TestGetAntivirusHealthCheckResult_ParticularPolicyCount() {
	policyResults := make([]*av.PolicyCheckResult, 5)
	for i := range policyResults {
		policyResults[i] = &av.PolicyCheckResult{
			Key: &jarvis.PolicyId{
				Id: int64(i),
			},
			PolicyName: fmt.Sprintf("Policy%d", i),
			Objects: []*jarvis.MapObjectKeyObjectValueEntry{
				{
					Key: &jarvis.ObjectKey{
						Project: &jarvis.SchemaValue_Project{
							Name: "antivirus",
						},
						ItemName: fmt.Sprintf("key%d", i),
					},
					Value: &jarvis.ObjectValue{
						Value: &jarvis.ObjectValue_StringScalar{
							StringScalar: &jarvis.String_Scalar{
								Value: fmt.Sprintf("value%d", i),
							},
						},
					},
				},
			},
			IsCascade: false,
		}
	}

	suite.mockPU.EXPECT().GetAntiVirusPoliciesPartiallyWithStaffid(suite.ctx, int64(20)).Return(policyResults, nil)
	suite.mockJMI.EXPECT().GetResult(gomock.Any()).Return(
		&av.HealthCheckResult{
			Policies: append(policyResults,
				&av.PolicyCheckResult{
					Key:        &jarvis.PolicyId{Id: 5},
					PolicyName: "ExtraPolicy5",
					Objects:    []*jarvis.MapObjectKeyObjectValueEntry{},
					IsCascade:  false,
				},
				&av.PolicyCheckResult{
					Key:        &jarvis.PolicyId{Id: 6},
					PolicyName: "ExtraPolicy6",
					Objects:    []*jarvis.MapObjectKeyObjectValueEntry{},
					IsCascade:  false,
				}),
		},
		&status.Status{Code: int32(codes.OK)},
	)

	req := &av.GetAntiVirusHealthCheckResult_Request{
		Keys: []*av.AntiVirusHealthCheckResult_Request{
			{
				CheckId: suite.checkRequestId,
				Offset:  0,
				Limit:   20,
			},
		},
	}
	resp, err := suite.hci.GetAntivirusHealthCheckResult(suite.ctx, req)
	suite.NoError(err)
	suite.Len(resp.Responses, 1)
	suite.Len(resp.Responses[0].Result.Policies, len(policyResults))
	suite.EqualValues(policyResults, resp.Responses[0].Result.Policies)
	suite.Equal(int32(codes.OK), resp.Responses[0].Status.Code)
}

func TestHealthCheckTestSuite(t *testing.T) {
	suite.Run(t, new(HealthCheckTestSuite))
}
