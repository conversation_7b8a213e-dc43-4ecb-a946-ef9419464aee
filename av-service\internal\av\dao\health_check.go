package dao

import (
	"time"
	"context"
	"strconv"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	timestamp "github.com/golang/protobuf/ptypes/timestamp"
	sorm "git-biz.qianxin-inc.cn/ats-rd2-infra-components/sdk/sorm.git/orm"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
	jarvis "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/jarvis"
	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	model "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/model"
	common "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/common"
)

type HealthCheckDao struct {
	Provider  server.ServiceProvider
	result2Model func (*av.HealthCheckResult) []*model.HealthCheck
	model2Result func ([]*model.HealthCheck) *av.HealthCheckResult
}

func NewHealthCheckDao(checkId int32, sp server.ServiceProvider) *HealthCheckDao {
	d := &HealthCheckDao{
		Provider: sp,
	}
	
	db := sorm.NewOrmWithDb(d.Provider.DefaultDB())
	if err := db.RegisterTable(new(model.HealthCheck)); err != nil {
		gflog.Errorf("RegisterTable HealthCheck is failed: %v ", err)
	}

	switch av.HealthCheckItem(checkId) {
	case av.HealthCheckItem_ACCESS_ClOUD:
		d.result2Model = AccessCloudCheckResult2Model
		d.model2Result = AccessCloudCheckModel2Result

	case av.HealthCheckItem_SEC_HIDDEN_DANGER:
		d.result2Model = SecHiddenDangerCheckResult2Model
		d.model2Result = SecHiddenDangerCheckModel2Result

	case av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER:
		d.result2Model = PerformanceHiddenDangerCheckResult2Model
		d.model2Result = PerformanceHiddenDangerCheckModel2Result

	case av.HealthCheckItem_VIRUSLIB_VERSION:
		d.result2Model = ViruslibVersionCheckResult2Model
		d.model2Result = ViruslibVersionCheckModel2Result

	default:
		d.result2Model = AccessCloudCheckResult2Model
		d.model2Result = AccessCloudCheckModel2Result
	}

	return d 
}

func AccessCloudCheckResult2Model(result *av.HealthCheckResult) []*model.HealthCheck {

	if result.GetPolicies() == nil || len(result.GetPolicies()) == 0 {
		return nil 
	}  

	ct := time.Now().Unix()
	if result.GetCheckTime() != nil {
		ct = result.GetCheckTime().Seconds
	} 

	checkItems := []*model.HealthCheck{}
	for i := 0; i < len(result.GetPolicies()); i++ {
		
		policy := result.GetPolicies()[i]
		for j := 0; j < len(policy.GetObjects()); j++ {
		
			object := policy.GetObjects()[j]
			check := &model.HealthCheck{
				CheckId: int32(result.GetCheckId()),
				CheckTime: ct,
				PolicyId: policy.GetKey().GetId(),
				PolicyName: policy.GetPolicyName(),      //策略名称
				PolicyCascade: policy.GetIsCascade(), //是否为上级策略 
				PolicyItemKey: object.GetKey().GetItemName(),//具体策略项Key
				PolicyItemValue: object.GetValue().GetStringScalar().GetValue(), //具体策略项Value
			}
			checkItems = append(checkItems, check)

		}
	}
	return checkItems
}

func AccessCloudCheckModel2Result(rs []*model.HealthCheck) *av.HealthCheckResult {
	if len(rs) < 1 {
		return nil
	}
	
	result := &av.HealthCheckResult{
		CheckId: av.HealthCheckItem_ACCESS_ClOUD,
		ItemName: "管理中心云查连通性检查",
	}

	var policies map[int64]struct{}
	policies = make(map[int64]struct{}, 1)
	
	for i := 0; i < len(rs); i++ {
		hc := rs[i]
		_, ok := policies[hc.PolicyId]
		if ok {
			continue //因为这一个检查项只有一个策略值，正常情况不太会有重复的。这里直接去重
		} else {
			policy := &av.PolicyCheckResult{
				Key: &jarvis.PolicyId{
					Id: hc.PolicyId,
				},
				PolicyName: hc.PolicyName,
				Objects: []*jarvis.MapObjectKeyObjectValueEntry{
					{
						Key: &jarvis.ObjectKey{
							Project: &jarvis.SchemaValue_Project{
								Name: "antivirus",
							},
							ItemName: hc.PolicyItemKey, 
						},
						Value: &jarvis.ObjectValue{
							Value: &jarvis.ObjectValue_StringScalar{
								StringScalar: &jarvis.String_Scalar{
									Value: hc.PolicyItemValue,
								},
							},
						},
					},
				},
				IsCascade: hc.PolicyCascade,
			}

			policies[hc.PolicyId] = struct{}{}
			result.Policies = append(result.Policies, policy)
			if result.CheckTime == nil {
				result.CheckTime = &timestamp.Timestamp{
					Seconds: hc.CheckTime,
				}
			} 
		}
	}

	return result
}

func SecHiddenDangerCheckModel2Result(rs []*model.HealthCheck) *av.HealthCheckResult {
	if len(rs) < 1 {
		return nil
	}
	
	result := &av.HealthCheckResult{
		CheckId: av.HealthCheckItem_SEC_HIDDEN_DANGER,
		ItemName: "病毒防护策略-部署阶段的配置项检查",
	}

	
	var policies = make(map[int64]*av.PolicyCheckResult, 1)
	
	for i := 0; i < len(rs); i++ {
		hc := rs[i]
		p, ok := policies[hc.PolicyId]
		if ok {
			//存在对应的policy的时候，只需要往数组后面加object即可
			p.Objects = append(p.Objects, &jarvis.MapObjectKeyObjectValueEntry{
				Key: &jarvis.ObjectKey{
					Project: &jarvis.SchemaValue_Project{
						Name: "antivirus",
					},
					ItemName: hc.PolicyItemKey, 
				},
				Value: &jarvis.ObjectValue{
					Value: &jarvis.ObjectValue_StringScalar{
						StringScalar: &jarvis.String_Scalar{
							Value: hc.PolicyItemValue,
						},
					},
				},
			})
			continue //因为这一个检查项只有一个策略值，正常情况不太会有重复的。这里直接去重
		} else {
			policy := &av.PolicyCheckResult{
				Key: &jarvis.PolicyId{
					Id: hc.PolicyId,
				},
				PolicyName: hc.PolicyName,
				Objects: []*jarvis.MapObjectKeyObjectValueEntry{
					{
						Key: &jarvis.ObjectKey{
							Project: &jarvis.SchemaValue_Project{
								Name: "antivirus",
							},
							ItemName: hc.PolicyItemKey, 
						},
						Value: &jarvis.ObjectValue{
							Value: &jarvis.ObjectValue_StringScalar{
								StringScalar: &jarvis.String_Scalar{
									Value: hc.PolicyItemValue,
								},
							},
						},
					},
				},
				IsCascade: hc.PolicyCascade,
			}

			policies[hc.PolicyId] = policy
			result.Policies = append(result.Policies, policy)
			if result.CheckTime == nil {
				result.CheckTime = &timestamp.Timestamp{
					Seconds: hc.CheckTime,
				}
			} 
		}
	}

	return result
}

func PerformanceHiddenDangerCheckModel2Result(rs []*model.HealthCheck) *av.HealthCheckResult {
	if len(rs) < 1 {
		return nil
	}
	
	result := &av.HealthCheckResult{
		CheckId: av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER,
		ItemName: "病毒防护策略-影响终端性能的配置项检查",
	}

	var policies map[int64]struct{}
	policies = make(map[int64]struct{}, 1)
	
	for i := 0; i < len(rs); i++ {
		hc := rs[i]
		_, ok := policies[hc.PolicyId]
		if ok {
			continue //因为这一个检查项只有一个策略值，正常情况不会有重复的。这里直接去重
		} else {
			val := hc.PolicyItemValue == "true"
			policy := &av.PolicyCheckResult{
				Key: &jarvis.PolicyId{
					Id: hc.PolicyId,
				},
				PolicyName: hc.PolicyName,
				Objects: []*jarvis.MapObjectKeyObjectValueEntry{
					{
						Key: &jarvis.ObjectKey{
							Project: &jarvis.SchemaValue_Project{
								Name: "antivirus",
							},
							ItemName: hc.PolicyItemKey, 
						},
						Value: &jarvis.ObjectValue{
							Value: &jarvis.ObjectValue_BoolScalar{
								BoolScalar: &jarvis.Bool_Scalar{
									Value: val,
								},
							},
						},
					},
				},
				IsCascade: hc.PolicyCascade,
			}

			policies[hc.PolicyId] = struct{}{}
			result.Policies = append(result.Policies, policy)
			if result.CheckTime == nil {
				result.CheckTime = &timestamp.Timestamp{
					Seconds: hc.CheckTime,
				}
			} 
		}
	}

	return result
}

func ViruslibVersionCheckModel2Result(rs []*model.HealthCheck) *av.HealthCheckResult {
	if len(rs) < 1 {
		return nil
	}
	
	result := &av.HealthCheckResult{
		CheckId: av.HealthCheckItem_VIRUSLIB_VERSION,
		ItemName: "病毒库陈旧引导提示",
		VirusLibs:  []*av.VirusLibCheckResult{},
	}

	for i := 0 ; i < len(rs); i++ {
		result.VirusLibs = append(result.VirusLibs, &av.VirusLibCheckResult{
			Days: rs[i].VirusLibPeriod,
			Version: rs[i].VirusLibVersion,
			VbType: av.VirusLibType_name[rs[i].VirusLibType],
			
		})	
		
		if result.CheckTime == nil {
			result.CheckTime = &timestamp.Timestamp{
				Seconds: rs[i].CheckTime,
			}
		}	
	}

	
	return result
}

func SecHiddenDangerCheckResult2Model(result *av.HealthCheckResult) []*model.HealthCheck {
	if result.GetPolicies() == nil || len(result.GetPolicies()) == 0 {
		return nil 
	}  

	ct := time.Now().Unix()
	if result.GetCheckTime() != nil {
		ct = result.GetCheckTime().Seconds
	} 
	
	checkItems := []*model.HealthCheck{}
	for i := 0; i < len(result.GetPolicies()); i++ {
		
		policy := result.GetPolicies()[i]
		objects := common.ParseSecObjects(policy.GetObjects())
		for k, v := range objects {
			
			value := ""
			if val, ok := v.IntValue(); ok {
				value = strconv.FormatInt(val, 10) // 将 int64 转换为十进制字符串
			} else if _, ok := v.BoolValue(); ok {
				value = "true"
			}

			check := &model.HealthCheck{
				CheckId: int32(result.GetCheckId()),
				CheckTime: ct,
				PolicyId: policy.GetKey().GetId(),
				PolicyName: policy.GetPolicyName(),      //策略名称
				PolicyCascade: policy.GetIsCascade(), //是否为上级策略 
				PolicyItemKey: k,//具体策略项Key
				PolicyItemValue: value, //具体策略项Value
			}
			checkItems = append(checkItems, check)
		}
	}
	return checkItems
}

func PerformanceHiddenDangerCheckResult2Model(result *av.HealthCheckResult) []*model.HealthCheck {

	if result.GetPolicies() == nil || len(result.GetPolicies()) == 0 {
		return nil 
	}  

	ct := time.Now().Unix()
	if result.GetCheckTime() != nil {
		ct = result.GetCheckTime().Seconds
	} 
	
	checkItems := []*model.HealthCheck{}
	for i := 0; i < len(result.GetPolicies()); i++ {
		
		policy := result.GetPolicies()[i]
		
		for j := 0; j < len(policy.GetObjects()); j++ {
		
			object := policy.GetObjects()[j]
			val := "true"
			check := &model.HealthCheck{
				CheckId: int32(result.GetCheckId()),
				CheckTime: ct,
				PolicyId: policy.GetKey().GetId(),
				PolicyName: policy.GetPolicyName(),      //策略名称
				PolicyCascade: policy.GetIsCascade(), //是否为上级策略 
				PolicyItemKey: object.GetKey().GetItemName(),//具体策略项Key
				PolicyItemValue: val, //具体策略项Value
			}
			checkItems = append(checkItems, check)

		}
	}
	return checkItems
}

func ViruslibVersionCheckResult2Model(result *av.HealthCheckResult) []*model.HealthCheck {
	checkItems := []*model.HealthCheck{}
	
	ct := time.Now().Unix()
	if result.GetCheckTime() != nil {
		ct = result.GetCheckTime().Seconds
	} 
	
	vb := result.VirusLibs
	for i := 0; i < len(vb); i++ {
		r := vb[i]
		checkItems = append(checkItems, &model.HealthCheck{
			CheckId: int32(av.HealthCheckItem_VIRUSLIB_VERSION),
			CheckTime: ct,
			VirusLibType: av.VirusLibType_value[r.VbType],
			VirusLibPeriod: r.Days, 
			VirusLibVersion: r.Version,
		})	
	}
	
	return checkItems
}

func (d *HealthCheckDao) GetDb() *sorm.Orm {
	return sorm.NewOrmWithDb(d.Provider.DefaultDB())
}

func (d *HealthCheckDao) UpdateResult(checkId int32, result *av.HealthCheckResult) error {
	
	if result != nil {
		newResults := d.result2Model(result)
		//gflog.Debugf("HealthCheckDao UpdateResult checkId=%v results=%v", result.GetCheckId(), newResults)
		
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		db := d.GetDb()
		db = db.WithContext(ctx)		
		err := db.DoTransaction(func() error {
			_, err := db.QueryTable(new(model.HealthCheck)).WithFilter("check_id", int32(result.GetCheckId())).QsDelete()
			if err != nil {
				gflog.Errorf("Delete check_id=%v failed err=%v",result.GetCheckId(), err)
				return err
			}

			//一次插入50条
			_, err = db.InsertMulti(50, &newResults)
			if err != nil {
				gflog.Errorf("InsertMulti failed err=%v",result.GetCheckId(), err)
			}
			return err
		})

		if err != nil {
			gflog.Errorf("HealthCheckDao UpdateResult failed err=%v", err)
		}

		return err
	}

	//result为nil的情况说明出现问题的场景已经没有了,只需要做一下清理就行。
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	db := d.GetDb()
	db = db.WithContext(ctx)		
	err := db.DoTransaction(func() error {
		_, err := db.QueryTable(new(model.HealthCheck)).WithFilter("check_id", checkId).QsDelete()
		if err != nil {
			gflog.Errorf("Delete check_id=%v failed err=%v", result.GetCheckId(), err)
		}

		return err
	})


	if err != nil {
		gflog.Errorf("HealthCheckDao UpdateResult failed err=%v", err)
	}

	return err
}

func (d *HealthCheckDao) GetResult(checkId int32, offset, limit int64) (*av.HealthCheckResult, error) {
	//查询的时候带上policyid
	queryResult := []*model.HealthCheck{}
	
	db := d.GetDb()
	qs := db.QueryTable(new(model.HealthCheck)).WithFilter("check_id", checkId)
	qs = qs.WithOrderBy("policy_id", 1).WithOffset(offset).WithLimit(limit)
	_, err := qs.All(&queryResult)
	if err != nil {
		gflog.Errorf("GetResult query check_id=%v failed err=%v", checkId, err)
		return nil, err
	}
	
	gflog.Infof("GetResult query check_id=%v result=%v", checkId, queryResult)

	result := d.model2Result(queryResult)
	if result == nil {
		return nil, status.Error(codes.NotFound, "result is empty")
	}

	return result, nil 
}


func (d *HealthCheckDao) GetAllResult(checkId int32) (*av.HealthCheckResult, error) {
	//查询的时候带上policyid
	queryResult := []*model.HealthCheck{}
	
	db := d.GetDb()
	qs := db.QueryTable(new(model.HealthCheck)).WithFilter("check_id", checkId)
	qs = qs.WithOrderBy("policy_id", 1)
	_, err := qs.All(&queryResult)
	if err != nil {
		gflog.Errorf("GetAllResult query check_id=%v failed err=%v", checkId, err)
		return nil, err
	}

	gflog.Infof("GetAllResult query check_id=%v result=%v", checkId, queryResult)

	result := d.model2Result(queryResult)
	if result == nil {
		return nil, status.Error(codes.NotFound, "result is empty")
	}

	return result, nil 
}