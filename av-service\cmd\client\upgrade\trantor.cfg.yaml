global.ca-cert: |
    -----BEGIN CERTIFICATE-----
    MIIFUzCCAzugAwIBAgIRAL7RpFnAVymURRNeQDsUAdcwDQYJKoZIhvcNAQELBQAw
    MTEMMAoGA1UEChMDUUFYMQwwCgYDVQQLEwNBVFMxEzARBgNVBAMTClFBWC1BVFMt
    Q0EwIBcNMjAwNDE2MDk0OTI4WhgPMjEyMDA0MTYwOTQ5MjNaMDExDDAKBgNVBAoT
    A1FBWDEMMAoGA1UECxMDQVRTMRMwEQYDVQQDEwpRQVgtQVRTLUNBMIICIjANBgkq
    hkiG9w0BAQEFAAOCAg8AMIICCgKCAgEA0StAWoyCmd4r0Kve6juxKfqAKqTbNc1w
    Eq6tNu1XwHjENYC+h3oHoW0HsKR/2XKK7w90YOJBMVRRmGVwD/1DIrBVljax+IOB
    0U4yu/jwk8YL8nHDoXTVkmBw+3U56+hQ2Qo8fd7E2ybiGVZQOSwzm1x1V0N4S67c
    lZEgqF8KaWIiriW6Dr+6WcwkLO2Sw9Bfze7Fk7K1cvRAyafLgwAyKhqTtUUAfbYk
    Z91V56xKPxOVzIQrI0v4aljrbA5ydTVgOnox812dEqp6mhK+M9LMwfESzrUB//EP
    NwmkYCIWpEXST6PmQDYq2eKFjLjB6U5/XnGfwrRo2VY/Z5cdJViQkzHYLvcfT1I7
    y8RedPlh/qM7PInyjXbRPfYNpQIQeWFnl5QEiEeVEFzrRlTgAHD52MeiWUXGY8X0
    cTD38+MWkmLJOvOoZcbPW93gkRPZHJy26M1gn2JG8jjzGD/iVdS6Pc6wKzjlbp5R
    0po8Ec412JDnbPP4n2pxuRCGioBUPDJXXXqbXATIwxqU3RlSP6qIj98YQFYbIThD
    w3c5twjETCOA3vW+FLHFxwi7jZwOEqL4BuK5025kRIM2vGJXobZGHFJonRM+kDor
    vNUrA8fqitQuHwxujzPcR0scJ52ZgCHU03fvO6yIIdG7y2uWrd/xx/2MM2O4eF85
    nb99YJTObQECAwEAAaNkMGIwDgYDVR0PAQH/BAQDAgG+MB0GA1UdJQQWMBQGCCsG
    AQUFBwMBBggrBgEFBQcDAjASBgNVHRMBAf8ECDAGAQH/AgEBMB0GA1UdDgQWBBSe
    U25ew5Uq6u4PPzigrO6ozpFQHjANBgkqhkiG9w0BAQsFAAOCAgEAfOoCXtyfcQ9K
    O0AazcGyYz8pNzLmsXQBHWsXDWjOj3XHbZrLFBdk/qmFD6P6txwvzyFWfu56lyOu
    5ot+HLyZ+YsihaV8t4HgK5EAvmw2ZM1SfvFnwuwSQ1B1/bkQZr4YBoTeG9qbmgGi
    6lwXxALBxZnbO8RlPLklAEdD+WMqV9+R3SwKMcHhg79Wq4LdnQKgKKbMlUalclc+
    kvkcNBT3h3eAiT3KYuIteozg3u8t0fLA8XR3gZtCyO2+kh5ij2Jeza/Vy/JyhP5E
    vvx+fGrHHU8EPdq99NkB17yTDEpYaemb84ePvwP9fPX+5xRAGCZoYB0TXjmBcLyy
    FmoNcAh/Q/V+iTHEIT3sa0QQApTygg5MkIRfIZhmatM7Ep+n0MXL/vjF/3J62zP4
    7ab6woaPsjlo9n2QSFvPWJMdEcOeO2IfPauDD5zHrr2mrFQezkDNpS7ibn51VzTp
    fwSxpA030dmzPgSiIwsImQ95N+dCU3W+0FvnSXDsgiszLi361DJC0BSOmpmtyWX/
    27OCOKxL3VTpvAizfZPTdyaj8zLnje+o1MR7Y9rHf1iP3hroEi3SSTw/nUQjG9H3
    x34UCzXUdfUInyo68mMZWJ7gGADdJWxOMpKvPLvIIqzZ+5MhvQvewak0BL5xBKEA
    FqDdpO4i4bnRo9O5l9rt2TVM+1sX728=
    -----END CERTIFICATE-----
mode.agent: true
#test02
#edge.addr: *************:32262
#cloud_qa
#edge.addr: *************:30045
#qa cluster
#edge.addr: *************:32245
#qax
#edge.addr: *************:32145
#dev cluster
#edge.addr: *************:30447
#dev01
#edge.addr: *************:30220
#darwin test
#edge.addr: *************:30755
#slim01 
#edge.addr: *************:6785
#edge.addr: ************:30081
#edge.addr: *************:30081
edge.addr: ************:6785
