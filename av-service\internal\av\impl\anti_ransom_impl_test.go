package impl

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/client"
	api "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/av_v1"
	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	model "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/model"
	"git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/zeus"
	empty "github.com/golang/protobuf/ptypes/empty"
)

func TestAntiRansomImpl_GetClientAntiransom_Unimplemented(t *testing.T) {
	// Create a minimal implementation that doesn't require database
	impl := &AntiRansomImpl{}
	ctx := context.Background()
	request := &api.GetClientAntiransom_Request{}

	response, err := impl.GetClientAntiransom(ctx, request)

	assert.Nil(t, response)
	assert.Error(t, err)

	// Check that it returns an Unimplemented error
	st, ok := status.FromError(err)
	assert.True(t, ok)
	assert.Equal(t, codes.Unimplemented, st.Code())
	assert.Contains(t, st.Message(), "未实现")
}

func TestAntiRansomImpl_BatchGetClientAntiransom_EmptyRequest(t *testing.T) {
	// Create a minimal implementation that doesn't require database
	impl := &AntiRansomImpl{}
	ctx := context.Background()
	request := &api.BatchGetClientAntiransom_Request{}

	response, err := impl.BatchGetClientAntiransom(ctx, request)

	// BatchGetClientAntiransom returns a response, not an error
	assert.NotNil(t, response)
	assert.NoError(t, err)
	assert.Empty(t, response.Status)
	assert.Empty(t, response.Headers)
	assert.Empty(t, response.Trailers)
	assert.Empty(t, response.Antiransoms)
}

func TestAntiRansomImpl_BatchGetClientAntiransom_WithInvalidKeys(t *testing.T) {
	// Create a minimal implementation that doesn't require database
	impl := &AntiRansomImpl{}
	ctx := context.Background()
	request := &api.BatchGetClientAntiransom_Request{
		Keys: []*api.GetClientAntiransom_Request{
			{
				Key: &client.ClientId{
					Id: "", // Invalid empty ID
					AssetId: &zeus.AssetId{
						Id:  1,
						Oid: 1,
					},
				},
			},
		},
	}

	response, err := impl.BatchGetClientAntiransom(ctx, request)

	// Should return a response with error status
	assert.NotNil(t, response)
	assert.NoError(t, err)
	assert.Len(t, response.Status, 1)
	assert.Equal(t, int32(codes.InvalidArgument), response.Status[0].Code)
	assert.Contains(t, response.Status[0].Message, "ClientId.Id is nil")
}

func TestAntiRansomImpl_PatchAntiransomStatistics_CreateDTO(t *testing.T) {
	// Test the DTO creation logic separately
	request := &av.PatchAntiRansomStatistics_Request{
		Key: &client.ClientId{
			Id: "test-client-id",
			AssetId: &zeus.AssetId{
				Id:  123,
				Oid: 456,
			},
		},
		UpdateTime:       1234567890,
		ProtectionStatus: 1,
		DetectionTimes:   10,
		ThreatEvents:     5,
		ProtectedTimes:   8,
		RecoveredFiles:   3,
	}

	// Simulate the DTO creation that happens in PatchAntiransomStatistics
	dto := &model.AntiRansomDTO{
		ClientId:         request.GetKey(),
		UpdateTime:       request.GetUpdateTime(),
		ProtectionStatus: request.GetProtectionStatus(),
		DetectionTimes:   request.GetDetectionTimes(),
		ThreatEvents:     request.GetThreatEvents(),
		ProtectedTimes:   request.GetProtectedTimes(),
		RecoveredFiles:   request.GetRecoveredFiles(),
	}

	assert.NotNil(t, dto)
	assert.Equal(t, request.GetKey(), dto.ClientId)
	assert.Equal(t, request.GetUpdateTime(), dto.UpdateTime)
	assert.Equal(t, request.GetProtectionStatus(), dto.ProtectionStatus)
	assert.Equal(t, request.GetDetectionTimes(), dto.DetectionTimes)
	assert.Equal(t, request.GetThreatEvents(), dto.ThreatEvents)
	assert.Equal(t, request.GetProtectedTimes(), dto.ProtectedTimes)
	assert.Equal(t, request.GetRecoveredFiles(), dto.RecoveredFiles)
}

func TestAntiRansomImpl_ErrorHandling(t *testing.T) {
	// Test that the implementation handles model.ErrAlreadyExist correctly
	// This is more of a documentation test since we can't easily mock the database operations

	assert.Equal(t, model.ErrAlreadyExist, model.ErrAlreadyExist)

	// Test empty response creation
	emptyResponse := &empty.Empty{}
	assert.NotNil(t, emptyResponse)
}
