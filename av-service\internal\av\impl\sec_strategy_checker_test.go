package impl

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
)

func TestSecStrategyJobChecker_Struct(t *testing.T) {
	// Test the struct fields exist and can be initialized
	checker := &SecStrategyJobChecker{
		CheckName:   "病毒防护策略-部署阶段的配置项检查",
		CheckId:     av.HealthCheckItem_SEC_HIDDEN_DANGER,
		processFlag: IDLE_JOB,
	}

	assert.Equal(t, "病毒防护策略-部署阶段的配置项检查", checker.CheckName)
	assert.Equal(t, av.HealthCheckItem_SEC_HIDDEN_DANGER, checker.CheckId)
	assert.Equal(t, int32(IDLE_JOB), checker.processFlag)
}

func TestSecStrategyJobChecker_GetJobStatus(t *testing.T) {
	checker := &SecStrategyJobChecker{
		processFlag: IDLE_JOB,
	}

	status := checker.GetJobStatus()
	assert.Equal(t, int32(IDLE_JOB), status)

	// Test different status values
	checker.processFlag = PORCESSING_JOB
	status = checker.GetJobStatus()
	assert.Equal(t, int32(PORCESSING_JOB), status)

	checker.processFlag = DEAD_JOB
	status = checker.GetJobStatus()
	assert.Equal(t, int32(DEAD_JOB), status)
}

func TestSecStrategyJobChecker_StopJob(t *testing.T) {
	checker := &SecStrategyJobChecker{
		stopCh: make(chan int, 1), // Buffered channel to avoid blocking
	}

	// This should not block or panic
	assert.NotPanics(t, func() {
		checker.StopJob()
	})

	// Verify that a signal was sent to the stop channel
	select {
	case signal := <-checker.stopCh:
		assert.Equal(t, 1, signal)
	case <-time.After(100 * time.Millisecond):
		t.Error("Expected signal on stop channel")
	}
}

func TestSecStrategyJobChecker_TriggerACheck(t *testing.T) {
	checker := &SecStrategyJobChecker{
		quickCh: make(chan int, 1), // Buffered channel to avoid blocking
	}

	// This should not block or panic
	assert.NotPanics(t, func() {
		checker.TriggerACheck()
	})

	// Verify that a signal was sent to the quick check channel
	select {
	case signal := <-checker.quickCh:
		assert.Equal(t, 1, signal)
	case <-time.After(100 * time.Millisecond):
		t.Error("Expected signal on quick check channel")
	}
}

func TestSecStrategyJobChecker_GetCheckResult_WithMockHealthCheck(t *testing.T) {
	// Create a mock HealthCheckModel
	mockHC := &MockHealthCheckModel{
		result: &av.HealthCheckResult{
			CheckId: av.HealthCheckItem_SEC_HIDDEN_DANGER,
		},
		err: nil,
	}

	checker := &SecStrategyJobChecker{
		hc: mockHC,
	}

	// This should not panic
	result, status := checker.GetCheckResult()

	// We expect some kind of result
	assert.NotNil(t, result)
	assert.NotNil(t, status)
	assert.Equal(t, av.HealthCheckItem_SEC_HIDDEN_DANGER, result.CheckId)
}

func TestSecStrategyJobChecker_StructValidation(t *testing.T) {
	// Test that the struct can be properly initialized
	checker := &SecStrategyJobChecker{
		CheckName:   "病毒防护策略-部署阶段的配置项检查",
		CheckId:     av.HealthCheckItem_SEC_HIDDEN_DANGER,
		processFlag: IDLE_JOB,
	}

	assert.Equal(t, "病毒防护策略-部署阶段的配置项检查", checker.CheckName)
	assert.Equal(t, av.HealthCheckItem_SEC_HIDDEN_DANGER, checker.CheckId)
	assert.Equal(t, int32(IDLE_JOB), checker.processFlag)
}

func TestSecStrategyJobChecker_ChannelInitialization(t *testing.T) {
	// Test that channels can be properly initialized
	checker := &SecStrategyJobChecker{
		stopCh:  make(chan int),
		quickCh: make(chan int),
	}

	assert.NotNil(t, checker.stopCh)
	assert.NotNil(t, checker.quickCh)

	// Test that channels are not closed initially
	select {
	case <-checker.stopCh:
		t.Error("Stop channel should not have data initially")
	case <-checker.quickCh:
		t.Error("Quick channel should not have data initially")
	default:
		// This is expected - channels should be empty
	}
}

func TestSecStrategyJobChecker_CheckIdValidation(t *testing.T) {
	checker := &SecStrategyJobChecker{
		CheckId: av.HealthCheckItem_SEC_HIDDEN_DANGER,
	}

	// Verify the CheckId is set correctly
	assert.Equal(t, av.HealthCheckItem_SEC_HIDDEN_DANGER, checker.CheckId)

	// Test that it's the expected enum value
	assert.Equal(t, av.HealthCheckItem(1), checker.CheckId) // SEC_HIDDEN_DANGER = 1
}

func TestSecStrategyJobChecker_ProcessFlagStates(t *testing.T) {
	checker := &SecStrategyJobChecker{}

	// Test all possible process flag states
	states := []int32{IDLE_JOB, PORCESSING_JOB, DEAD_JOB}

	for _, state := range states {
		checker.processFlag = state
		assert.Equal(t, state, checker.GetJobStatus())
	}
}

func TestSecStrategyJobChecker_CheckNameValidation(t *testing.T) {
	checker := &SecStrategyJobChecker{
		CheckName: "病毒防护策略-部署阶段的配置项检查",
	}

	// Verify the check name is in Chinese and contains expected keywords
	assert.Contains(t, checker.CheckName, "病毒防护策略")
	assert.Contains(t, checker.CheckName, "配置项检查")
	assert.Contains(t, checker.CheckName, "部署阶段")
}

func TestSecStrategyJobChecker_InterfaceCompliance(t *testing.T) {
	// Test that SecStrategyJobChecker implements JobChecker interface
	var _ JobChecker = &SecStrategyJobChecker{}

	// This test will fail to compile if the interface is not properly implemented
	checker := &SecStrategyJobChecker{
		stopCh:      make(chan int, 1),
		quickCh:     make(chan int, 1),
		processFlag: IDLE_JOB,
		hc: &MockHealthCheckModel{
			result: &av.HealthCheckResult{
				CheckId: av.HealthCheckItem_SEC_HIDDEN_DANGER,
			},
			err: nil,
		},
	}

	// Test that interface methods can be called without panic
	assert.NotPanics(t, func() {
		// Don't call checkLoop() as it runs indefinitely
		checker.StopJob()
		checker.TriggerACheck()
		result, status := checker.GetCheckResult()
		_ = result
		_ = status
		jobStatus := checker.GetJobStatus()
		_ = jobStatus
	})
}
