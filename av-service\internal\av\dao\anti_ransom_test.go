package dao

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
	model "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/model"
	"git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/client"
	"git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/zeus"
)

// MockServiceProvider is a mock implementation of server.ServiceProvider
type MockServiceProvider struct {
	ctrl     *gomock.Controller
	recorder *MockServiceProviderMockRecorder
}

type MockServiceProviderMockRecorder struct {
	mock *MockServiceProvider
}

func NewMockServiceProvider(ctrl *gomock.Controller) *MockServiceProvider {
	mock := &MockServiceProvider{ctrl: ctrl}
	mock.recorder = &MockServiceProviderMockRecorder{mock}
	return mock
}

func (m *MockServiceProvider) EXPECT() *MockServiceProviderMockRecorder {
	return m.recorder
}

func (m *MockServiceProvider) DefaultDB() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DefaultDB")
	ret0, _ := ret[0].(interface{})
	return ret0
}

func TestNewAntiRansomDao(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockSP := NewMockServiceProvider(ctrl)
	mockSP.EXPECT().DefaultDB().Return(nil).AnyTimes()

	dao := NewAntiRansomDao(mockSP)

	assert.NotNil(t, dao)
	assert.Equal(t, mockSP, dao.Provider)
}

func TestAntiRansomDao_GetDb(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockSP := NewMockServiceProvider(ctrl)
	mockSP.EXPECT().DefaultDB().Return(nil).AnyTimes()

	dao := NewAntiRansomDao(mockSP)
	
	// This should not panic
	assert.NotPanics(t, func() {
		db := dao.GetDb()
		assert.NotNil(t, db)
	})
}

func TestAntiRansomDTO_ConvertOrm_Integration(t *testing.T) {
	dto := &model.AntiRansomDTO{
		ClientId: &client.ClientId{
			Id: "test-client-id",
			AssetId: &zeus.AssetId{
				Id:  123,
				Oid: 456,
			},
		},
		UpdateTime:       **********,
		ProtectionStatus: 1,
		DetectionTimes:   10,
		ThreatEvents:     5,
		ProtectedTimes:   8,
		RecoveredFiles:   3,
	}

	orm := dto.ConvertOrm()

	assert.NotNil(t, orm)
	assert.Equal(t, "test-client-id", orm.ClientId)
	assert.Equal(t, int64(123), orm.AssetId)
	assert.Equal(t, int64(456), orm.AssetOid)
	assert.Equal(t, int64(**********), orm.UpdateTime)
	assert.Equal(t, int32(1), orm.ProtectionStatus)
	assert.Equal(t, int64(10), orm.DetectionTimes)
	assert.Equal(t, int64(5), orm.ThreatEvents)
	assert.Equal(t, int64(8), orm.ProtectedTimes)
	assert.Equal(t, int64(3), orm.RecoveredFiles)
}

func TestAntiRansomDao_ValidateDTO(t *testing.T) {
	tests := []struct {
		name  string
		dto   *model.AntiRansomDTO
		valid bool
	}{
		{
			name: "Valid DTO",
			dto: &model.AntiRansomDTO{
				ClientId: &client.ClientId{
					Id: "test-client-id",
					AssetId: &zeus.AssetId{
						Id:  123,
						Oid: 456,
					},
				},
				UpdateTime:       **********,
				ProtectionStatus: 1,
				DetectionTimes:   10,
				ThreatEvents:     5,
				ProtectedTimes:   8,
				RecoveredFiles:   3,
			},
			valid: true,
		},
		{
			name: "Nil ClientId",
			dto: &model.AntiRansomDTO{
				ClientId:         nil,
				UpdateTime:       **********,
				ProtectionStatus: 1,
			},
			valid: false,
		},
		{
			name: "Empty ClientId.Id",
			dto: &model.AntiRansomDTO{
				ClientId: &client.ClientId{
					Id: "",
					AssetId: &zeus.AssetId{
						Id:  123,
						Oid: 456,
					},
				},
				UpdateTime:       **********,
				ProtectionStatus: 1,
			},
			valid: false,
		},
		{
			name: "Nil AssetId",
			dto: &model.AntiRansomDTO{
				ClientId: &client.ClientId{
					Id:      "test-client-id",
					AssetId: nil,
				},
				UpdateTime:       **********,
				ProtectionStatus: 1,
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.valid {
				assert.NotPanics(t, func() {
					orm := tt.dto.ConvertOrm()
					assert.NotNil(t, orm)
				})
			} else {
				// For invalid DTOs, we expect the conversion to still work
				// but may result in empty/zero values
				assert.NotPanics(t, func() {
					orm := tt.dto.ConvertOrm()
					assert.NotNil(t, orm)
				})
			}
		})
	}
}
