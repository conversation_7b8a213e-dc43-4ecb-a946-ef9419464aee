def gitRepoToJenkinsJob(gitRepo) {
  def (repo, branch) = gitRepo.tokenize(':')
  if (!branch) {
    branch = env.BRANCH_NAME
  }
  return repo.replaceAll('/', '-') + '/' + branch.replaceAll('/', '%252F')
}

def getgitrepo(){
    def repourl = env.GIT_URL
    println(repourl)
    def repourlnew = ""
    if (repourl.indexOf('git@') != -1) {
        println("git@----")
        def repourltemp = repourl.replace(":","/").replace(".git","")
        repourlnew = repourltemp.replace("git@","https://")
        println(repourlnew)
    } else {
        println("https://-----")
        repourlnew  = repourl.replace(".git","")
        println(repourlnew)
    }
    return repourlnew
}

def getsonardata() {
    def branch = env.JOB_BASE_NAME
    println("repourl:----")
    def repourl = env.GIT_URL
    println(repourl)
    def repourlnew = ""
    if (repourl.indexOf('git@') != -1) {
        def repourltemp = repourl.replace(":","/").replace(".git","")
        repourlnew = repourltemp.replace("git@","https://")
        println(repourlnew)
    } else {
        repourlnew  = repourl.replace(".git","")
    }
    def apiurl = "https://fusion.qianxin-inc.cn/pipeline/track/pipelines/data/sonar-info?repository="+ repourlnew +"&branch=" + branch
    def conn = new URL(apiurl).openConnection()
    conn.setRequestMethod('GET')
    conn.setDoOutput(true)
    def postRC = conn.getResponseCode()
    def result = conn.getInputStream().getText()
    data = result.tokenize(" ")
    println(data)
    return data
}


pipeline {
  agent {     // 定义使用的节点
    kubernetes {  // 定义使用kubernetes 动态创建slave 节点，slave节点可以支持linux、windows
      // defaultContainer 'golang-build'    // 定义执行shell的默认的container，假设POD的yaml中定义了多个container
      yaml """\
        apiVersion: v1
        kind: Pod
        metadata:
          labels:
            jenkins-job: mydemo
        spec:
          securityContext:
            runAsUser: 0
            fsGroup: 0
            runAsGroup: 0
          containers:
          - name: golang-build
            image: 'qianxin-image-local.af-biz.qianxin-inc.cn/qax-cicd/jenkins-slave-golang-dind:centos7.8-v1.24.3-20250521'
            command:
            - cat
            tty: true
            volumeMounts:
              - mountPath: /var/run/docker.sock
                name: docker-sock
                readOnly: false
              - mountPath: "/var/lib/docker"
                name: docker-directory
                readOnly: false
              - mountPath: "/data/golang"
                name: golang-data
          - name: sonar-check
            image: 'qianxin-image-local.af-biz.qianxin-inc.cn/qax-cicd/jenkins-slave-golang-dind:centos7.8-v1.16-20211203-4'
            command:
            - cat
            tty: true
          - name: unit-test
            image: 'qianxin-image-local.af-biz.qianxin-inc.cn/qax-cicd/mongo:3.6'
            tty: true
          volumes:
            - name: golang-data
              hostPath:
               path: /data/jenkins-slave-data/golang
               type: Directory
            - name: docker-sock
              hostPath:
                path: "/var/run/docker.sock"
            - name: docker-directory
              hostPath:
                path: "/var/lib/docker"
        """.stripIndent()
    }
  }
  parameters {
        string(name: 'PRS_PARAMS', defaultValue: '', description: 'prs扩展参数')
        string(name: 'EXTEND_PARAMS', defaultValue: '', description: '扩展参数')
        string(name: 'COMMITID', defaultValue: '', description: '需要构建代码的commit_id')
        string(name: 'TYPE', defaultValue: '', description: '需要构建架构的类型如：linux、windows')
        string(name: 'MIPS_BUILD', defaultValue: 'false', description: 'MISP构建等于true')
        string(name: 'MIPS_COMMIT', defaultValue: '', description: 'MIPS构建时的commit_id')
        string(name: 'IMAGE_FROM', defaultValue: '', description: '镜像信息')
        string(name: 'DOCKER_IMAGE', defaultValue: '', description: '镜像')
    }
  environment {
    // Static ENV (KEEP) ss
    TZ="Asia/Shanghai"
    // http_proxy = "http://static-proxy.g0.qianxin-inc.cn:3128"                                  // 加速某些网络的构建速度
    // https_proxy = "http://static-proxy.g0.qianxin-inc.cn:3128"                                 // 同上
    no_proxy = "api.darwin.qianxin-inc.cn,fusion.qianxin-inc.cn,goproxy.qianxin-inc.cn,git-biz.qianxin-inc.cn,git-open.qianxin-inc.cn,minio.ops.qianxin-inc.cn,ark.ops.qianxin-inc.cn,darwin.qianxin.com,harbor.ops.qianxin-inc.cn,darwin.qianxin.com,auto01v.zgb.shyc3.qianxin-inc.cn,k8s02.ops.zzyc.qianxin-inc.cn,*************,*************,lxs3.b.qianxin-inc.cn,darwin.b.qianxin.com,af-biz.qianxin-inc.cn,bom.qianxin-inc.cn"
    GIT_SSH_COMMAND ="ssh -o StrictHostKeyChecking=no -o User=git"                       // 避免slave节点首次连接git时出现 askpass，导致拉取依赖失败
    //SONAR_HOST_URL = "https://fusion-sonar.qianxin-inc.cn/"
    //SONAR_TOKEN = "****************************************"
    //SONAR_EXCLUSIONS = ''
    DOCKER_HARBOR_CREDS = credentials('harbor-qianxin-inc-cn')
    DOCKER_HARBOR_SERVER = "harbor.ops.qianxin-inc.cn"
    GIT_COMMIT_TIME = sh(returnStdout: true, script: 'git --no-pager log -n 1 --pretty=format:%ad --date=format:%Y%m%d%H%M%S').trim()
    GIT_REMOTE_URL = sh(returnStdout: true, script: 'git config remote.origin.url').trim()
    GIT_REMOTE_HTTPS = GIT_REMOTE_URL.replaceAll('git@', 'https://').replaceAll('cn:', 'cn/')
    GIT_SHORT_COMMIT = "${GIT_COMMIT[0..12]}"
    CI_COMMIT_SHORT_SHA = "${GIT_COMMIT.take(8)}"
    GOSUMDB = "off"
    GOPRIVATE = "git-biz.qianxin-inc.cn,git-open.qianxin-inc.cn"
    GOFLAGS = "-mod=mod" 
    CGO_ENABLED = "1"
    GODEBUG = "x509ignoreCN=0"
    //GOPROXY = "https://proxy.golang.com.cn,direct"
    GOPROXY = "https://af-biz.qianxin-inc.cn/artifactory/api/go/test-go-virtual,direct"
    RECEIVE_EMAIL_LIST = "<EMAIL>"
    GOTRACEBACK= "single"
    CODEQL_COMMAND = sh(script: "grep -o 'go build .*' arkbuild/ark.y*ml | head -n 1 | tr -d \"'\\\"\"", returnStdout: true).trim()

    // Project Custom ENV (Custom)
    PROJECT_NAME = sh(returnStdout: true, script: 'cat arkbuild/ark.y*ml | grep "^repo" | cut -d" " -f2 | uniq').trim()
    PROJECT_SERVICE_NAME = sh(returnStdout: true, script: 'cat arkbuild/ark.y*ml | sed -n "/^app_name: /p" | cut -d" " -f2 | uniq').trim()
    DEPLOY_TO_DEV_NAME = '天擎V10安全防护预研'
    DEPLOY_TO_DEV_PROJECT_NAME = '天擎'
    BUILD_SSH_KEY_ID = "build-skylar-ssh"
    TESTING_CENTER = ''
    LINUX_BUILD = "true"
    WINDOWS_BUILD = "true"
    ARM_BUILD = "true"
    MIPS_BUILD = "false"
    LOONG_BUILD = "true"
    TESTING_COVERAGE = "true"
    ISBINARY = "false"
    TESTCASE_REPOS = 'QAX-platform-QA-trantorbase:master'
    DOWNSTREAM_REPOS = ''
    DEV_DEPLOY_BRANCH_PATTERN = 'feature-10.8.0.2000'
    AUTO_TEST_BRANCH_PATTERN = 'feature-10.8.0.2000'
    LINUX_DARWIN_ARGS = ''
    WINDOWS_DARWIN_ARGS = ''
    LINUX_ARM_DARWIN_ARGS = ''
    LINUX_MIPS_DARWIN_ARGS = ''
    LINUX_LOONG_DARWIN_ARGS = ''
    IS_MIGRATION = 'false'
    IS_EXPORT_PRS = "true"
    GO_BUILD_COMMAND = "darwin-ci -build_id=${BUILD_ID}  -build_type=build  -execute_type=build_amd64 -job_name=${PROJECT_SERVICE_NAME} ${LINUX_DARWIN_ARGS}"
    GO_TEST_COMMAND = "make test"
    DATE = sh(returnStdout: true, script: 'date +%m%d').trim()
    GIT_BRANCH_SLUG = GIT_BRANCH.replaceAll('origin', '').replaceAll('/', '-')
    IMAGE_TAG = "${DATE}.${GIT_BRANCH_SLUG}_${CI_COMMIT_SHORT_SHA}.${BUILD_ID}"
    ARM_IMAGE_TAG = "${DATE}.${GIT_BRANCH_SLUG}_${CI_COMMIT_SHORT_SHA}.${BUILD_ID}_arm64"
    MIPS_IMAGE_TAG = "${DATE}.${GIT_BRANCH_SLUG}_${CI_COMMIT_SHORT_SHA}.${BUILD_ID}_mips64"
    LOONG_IMAGE_TAG = "${DATE}.${GIT_BRANCH_SLUG}_${CI_COMMIT_SHORT_SHA}.${BUILD_ID}_loong64"
    
    GIT_LOG = sh(returnStdout: true, script: 'git log -1 --pretty=format:"%s"').trim()
    GIT_COMMITTER_EMAIL = sh(returnStdout: true, script:'git log -1 --pretty=format:%ae')
    DOCKER_REPOSITORY = ''
    GENERIC_REPOSITORY = ''
    AF_AKPASSWROD_CREDS = credentials('af-akpassword-creds')

  }

  // Checkout code from git (KEEP)
  stages {
    // Reporting task start time （KEEP）
    stage('Pre Task') {                                                                                                                   // 通过stage 定义一个阶段，固定写法
      steps {                                                                                                                             // 通过steps 定义一个步骤
        container('golang-build') {
          withCredentials(bindings: [sshUserPrivateKey(credentialsId: "${BUILD_SSH_KEY_ID}", keyFileVariable: 'SSH_KEY_FOR_GOMOD')]) {      // 绑定ssh key，方便从环境变量中引用jenkins上的ssh key文件
            sh '''
              TASK_BEGEN_TIME=$((`date +%s%N` / 1000000))
              wget https://lxs3.b.qianxin-inc.cn/darwintest/build/ci/darwin-ci?id=${BUILD_ID} -O /bin/darwin-ci
              chmod +x /bin/darwin-ci
                 curl -k --location --request POST 'https://api.darwin.qianxin-inc.cn/CommitPreCreateV1/CreateCommitPreCreateMeta' \
                                          --header 'debug_tag: prs' \
                                          --header 'Content-Type: application/json' \
                                          --data-raw '{
                                              "arg":{
                                                  "commit_id": "'${CI_COMMIT_SHORT_SHA}'",
                                                  "git_addr": "'${GIT_REMOTE_HTTPS}'",
                                                  "is_migration":'${IS_MIGRATION}',
                                                  "branch": "'${GIT_BRANCH}'",
                                                  "build_id":"'${BUILD_ID}'",
                                                  "commiter": "'${GIT_COMMITTER_EMAIL}'",
                                                  "commit_log":"'"${GIT_LOG}"'"
                                              }
                                          }'

            '''
          }
        }
      }
    }

    // Code quality analysis （KEEP)
    stage('set env') {
      steps {
        catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
        script {
          println("---------TESTING_COVERAGE---+--${TESTING_COVERAGE}---")
          try {
               if (IS_MIGRATION == "true" ) {
                  TESTING_COVERAGE = "false"
                  println("---------IS_MIGRATION---+--${IS_MIGRATION}---")
                }
            } catch (MissingPropertyException ex) {
                println("变量IS_MIGRATION未定义")
            }
          println("---------TESTING_COVERAGE-----+${TESTING_COVERAGE}+------")
          if (TYPE != '') {
            WINDOWS_BUILD="false"
            LINUX_BUILD="false"
            ARM_BUILD="false"
            MIPS_BUILD="false"
            LOONG_BUILD="false"
            print("call rebuild ...${WINDOWS_BUILD}")
            if (TYPE == 'linux') {
              LINUX_BUILD = "true"
              println("rebuild type is linux")
            }
            if (TYPE == 'windows') {
              WINDOWS_BUILD = "true"
              println("rebuild type is windows--${WINDOWS_BUILD}")
            }
            if (TYPE == 'mips') {
              MIPS_BUILD = "true"
              println("rebuild type is mips")
            }
            if (TYPE == 'arm') {
              ARM_BUILD = "true"
              println("rebuild type is arm--${ARM_BUILD}")
            }
            if (TYPE == 'loong64') {
              LOONG_BUILD = "true"
              println("rebuild type is loong64--${LOONG_BUILD}")
            }
            print("windows:${WINDOWS_BUILD}--linux:${LINUX_BUILD}--arm:${ARM_BUILD}--mips:${MIPS_BUILD}--loong:${LOONG_BUILD}")
          } else {
            WINDOWS_BUILD = "${WINDOWS_BUILD}"
            LINUX_BUILD = "${LINUX_BUILD}"
            ARM_BUILD = "true"
            MIPS_BUILD = "${MIPS_BUILD}"
            LOONG_BUILD = "true"
          }
        }
      }
      }
   }


    // Build app from code (Custom)
    stage('unit test ') {
          environment {
            GOFLAGS = "-mod=mod" 
            GOPATH = "/data/golang"
            PATH = "/bin:/usr/local/go/bin:/usr/local/openjdk-8/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/local/sonar/bin"
          }
         
          steps {
            catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
            container('golang-build') {
              withCredentials(bindings: [sshUserPrivateKey(credentialsId: "${BUILD_SSH_KEY_ID}", keyFileVariable: 'SSH_KEY_FOR_GOMOD')]) {            // 绑定ssh key，ID可以从jenkins上获取，不要使用带密码的key
               
                sh '''
                         if [[ "${GO_TEST_COMMAND}" != "" ]]
                      then
                         set +e
                         # 生成私钥并放入默认的位置
                        TASK_BEGEN_TIME=$((`date +%s%N` / 1000000))
                        export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o User=git"
                        git config --global url."**************************:".insteadOf "https://git-biz.qianxin-inc.cn/"
                        mkdir -p  ~/.ssh
                        chmod 700 ~/.ssh
                        cat ${SSH_KEY_FOR_GOMOD} > ~/.ssh/id_rsa
                        chmod 400 ~/.ssh/id_rsa

                        if [ "${COMMITID}" != "" ]
                        then
                          git checkout "${COMMITID}"
                          echo "Testing checkout commitid!!!"
                        fi
                        eval "$GO_TEST_COMMAND"  | tee test.out
                        

                        go tool cover -func=./allcover.out -o ./allcover.txt

                        
                        
                         wget https://af-biz.qianxin-inc.cn:443/artifactory/test_release/unittest_tools/goLangUnitTest.py
                         
 

                            output=$(tail -n +2 ./allcover.out | awk '$3>0 {c+=$2} {t+=$2} END {printf "%d\t%d", c, t}')
                            numerator=$(echo "$output" | cut -f1)
                            denominator=$(echo "$output" | cut -f2)
                            
                            echo "a: $denominator"
                            echo "s: $numerator"
                         
                        
                        
                        chmod +x  goLangUnitTest.py  

                                                          
                     
           python  goLangUnitTest.py   "$GO_TEST_COMMAND"  ${GIT_REMOTE_URL}   ${GIT_BRANCH}  ${GIT_COMMIT}  ${GIT_COMMITTER_EMAIL}  ${TASK_BEGEN_TIME}   ${BUILD_URL}     
           caseNumber=$( grep '=== RUN' test.out | wc -l )
            param='{"repository":"'${GIT_REMOTE_URL}'","branch":"'${GIT_BRANCH}'","commitSha":"'${GIT_COMMIT}'","caseNumber":"'$caseNumber'","checkPointNumber":null}'
              echo "param=$param"
             curl  -k --noproxy '*' -sS -X POST \
             -H "Content-Type: application/json" \
             -d "$param" "https://fusion.qianxin-inc.cn/pipeline/track/pipelines/data/sonar-case-info"
  
                      else
             param='{"repository":"'${GIT_REMOTE_URL}'","branch":"'${GIT_BRANCH}'","commitSha":"'${GIT_COMMIT}'","commitEmail":"'${GIT_COMMITTER_EMAIL}'","jobUrl":"'${BUILD_URL}'","commitTimestamp":"'${TASK_BEGEN_TIME}'"}'
              echo "param=$param"
             curl  -k --noproxy '*' -sS -X POST \
             -H "Content-Type: application/json" \
             -H "X-DTC-Token: 2jzGOFSmuLnpciIi2Yq6SVmO" \
             -d "$param" "https://dtc.qianxin-inc.cn/gateway/receive/test-exception"
                   
                      fi
             if   [[ "${GO_TEST_COMMAND}" != "" ]] &&  [[ $JOB_NAME == *zion* || $JOB_NAME == *sccrdt* || $joburl == *zion-platform* ]]; then
                         
             python  goLangUnitTest.py   "$GO_TEST_COMMAND"  ${GIT_REMOTE_URL}   ${GIT_BRANCH}  ${GIT_COMMIT}  ${GIT_COMMITTER_EMAIL}  ${TASK_BEGEN_TIME}   ${BUILD_URL}  ${numerator}   ${denominator} 
             caseNumber=$( grep '=== RUN' test.out | wc -l )
               param='{"repository":"'${GIT_REMOTE_URL}'","branch":"'${GIT_BRANCH}'","commitSha":"'${GIT_COMMIT}'","caseNumber":"'$caseNumber'","checkPointNumber":null}'
              echo "param=$param"
             curl  -k --noproxy '*' -sS -X POST \
             -H "Content-Type: application/json" \
             -d "$param" "https://fusion.qianxin-inc.cn/pipeline/track/pipelines/data/sonar-case-info"
                   echo "param=$param"  

                         else
             param='{"repository":"'${GIT_REMOTE_URL}'","branch":"'${GIT_BRANCH}'","commitSha":"'${GIT_COMMIT}'","commitEmail":"'${GIT_COMMITTER_EMAIL}'","jobUrl":"'${BUILD_URL}'","commitTimestamp":"'${TASK_BEGEN_TIME}'"}'
              echo "param=$param"
             curl  -k --noproxy '*' -sS -X POST \
             -H "Content-Type: application/json" \
             -H "X-DTC-Token: 2jzGOFSmuLnpciIi2Yq6SVmO" \
             -d "$param" "https://dtc.qianxin-inc.cn/gateway/receive/test-exception"
                   

                        fi

                     '''
                }
              }
            }
          }
        }

    stage('Build app') {

      parallel {
  
        stage('Build app On Linux') {
          environment {
            GOFLAGS = "-mod=mod" 
            GOPATH = "/data/golang"
            PATH = "/bin:/usr/local/go/bin:/usr/local/openjdk-8/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/local/sonar/bin"
          }
          when { expression { LINUX_BUILD == 'true'  &&  params.IMAGE_FROM ==  '' && params.DOCKER_IMAGE ==  ''   }}
          steps {
            container('golang-build') {
              withCredentials(bindings: [sshUserPrivateKey(credentialsId: "${BUILD_SSH_KEY_ID}", keyFileVariable: 'SSH_KEY_FOR_GOMOD')]) {            // 绑定ssh key，ID可以从jenkins上获取，不要使用带密码的key
               
                  sh '''
                        # 下载脚本，并发送任务开始信息到聚变服务端
                        source /root/.cargo/env 
                        # 生成私钥并放入默认的位置
                        export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o User=git"
                        git config --global url."**************************:".insteadOf "https://git-biz.qianxin-inc.cn/"
                        mkdir -p  ~/.ssh
                        chmod 700 ~/.ssh
                        cat ${SSH_KEY_FOR_GOMOD} > ~/.ssh/id_rsa
                        chmod 400 ~/.ssh/id_rsa

                         # 下载jfrog 客户端
                     # curl -fL https://getcli.jfrog.io | sh
                      wget https://af-biz.qianxin-inc.cn/artifactory/qianxin-generic-release-zgb/dev-tools/jfrog
                     export CI=true
                      chmod +x jfrog
                     ./jfrog config remove qax
                     ./jfrog config add qax --artifactory-url=https://af-biz.qianxin-inc.cn/artifactory --user=$AF_AKPASSWROD_CREDS_USR --password=$AF_AKPASSWROD_CREDS_PSW
                     ./jfrog rt bce ${PROJECT_SERVICE_NAME} ${BUILD_ID}
                     ./jfrog rt go-config --server-id-resolve=qax  --repo-resolve=test-go-virtual
                   ##   ./jfrog rt go mod download --build-name=${PROJECT_SERVICE_NAME} --build-number=${BUILD_ID}
                     ./jfrog rt bp ${PROJECT_SERVICE_NAME} ${BUILD_ID}
                        
                      VERSION=${DATE}.${GIT_BRANCH_SLUG}_${CI_COMMIT_SHORT_SHA}.${BUILD_ID}                      
                      commitid="${COMMITID}"
                      echo "commitid------:${COMMITID}"
                      if [[ "${COMMITID}" != "" ]]
                      then
                        git checkout "${COMMITID}"
                        echo "Testing checkout commitid!!!"
                        VERSION=${DATE}.${GIT_BRANCH_SLUG}_${COMMITID}.${BUILD_ID}
                        echo "VERSION: ${VERSION}"
                      else
                        commitid=${CI_COMMIT_SHORT_SHA}
                      fi
                      if [ ${ISBINARY} == "true" ]; then
                        eval "$GO_BUILD_COMMAND" 
                        #上报prs
                        wget https://af-biz.qianxin-inc.cn:443/artifactory/test_release/prs_import
                        chmod +x  prs_import
                        ./prs_import -app_name=${PROJECT_SERVICE_NAME} -git_branch=${BRANCH_NAME} -git_id=${GIT_REMOTE_URL} -online=true -os=linux -arch=x86_64 -artifiact_type=docker -deploy_type=cluster
                      else
                          # 生成docker镜像的 tag,建议规则是 "当前日期.分支名_CommitID.BuildID"

                        echo "$DOCKER_HARBOR_CREDS_PSW" | docker login -u "$DOCKER_HARBOR_CREDS_USR" "$DOCKER_HARBOR_SERVER" --password-stdin
                        
                        rm -rf go.sum

                        # 镜像上传指令
                        echo 'ArkServerAddr = "https://ark.ops.qianxin-inc.cn:443"' > $HOME/.ark/config.toml

                        darwin-ci  -docker_warehouse_path=${DOCKER_REPOSITORY}   -binary_warehouse_path=${GENERIC_REPOSITORY}    -build_id=${BUILD_ID} -prs_params=${PRS_PARAMS} -docker_harbor=${DOCKER_HARBOR_SERVER} -docker_version=${IMAGE_TAG} -execute_type=linux -job_name=${PROJECT_SERVICE_NAME}  -git_addr=${GIT_REMOTE_URL} -branch=${BRANCH_NAME} -commit_id=${commitid} -git_commit_log=${GITCOMMITLOG} -is_export_prs=${IS_EXPORT_PRS} $LINUX_DARWIN_ARGS
                      fi
                     '''
                
              }
            }
          }
        }
    stage('Testing coverage') {
          environment {
            GOFLAGS = "-mod=mod" 
            GOPATH = "/data/golang"
            PATH = "/bin:/usr/local/go/bin:/usr/local/openjdk-8/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/local/sonar/bin"
          }
         when { expression { TESTING_COVERAGE == 'true'  &&  params.IMAGE_FROM ==  '' && params.DOCKER_IMAGE ==  ''    }}
        
        agent {     // 定义使用的节点
          kubernetes {  // 定义使用kubernetes 动态创建slave 节点，slave节点可以支持linux、windows
          // defaultContainer 'golang-build'    // 定义执行shell的默认的container，假设POD的yaml中定义了多个container
            yaml """\
              apiVersion: v1
              kind: Pod
              metadata:
                labels:
                  jenkins-job: mydemo
              spec:
                securityContext:
                  runAsUser: 0
                containers:
                - name: golang-build-coverage
                  image: 'qianxin-image-local.af-biz.qianxin-inc.cn/qax-cicd/jenkins-slave-golang-dind:centos7.8-v1.23.2-cover-20241023'
                  command:
                  - cat
                  tty: true
                  volumeMounts:
                    - mountPath: /var/run/docker.sock
                      name: docker-sock
                      readOnly: false
                    - mountPath: "/var/lib/docker"
                      name: docker-directory
                      readOnly: false
                    - mountPath: "/data/golang"
                      name: golang-data
                volumes:
                  - name: golang-data
                    hostPath:
                      path: /data/jenkins-slave-data/golang
                      type: Directory
                  - name: docker-sock
                    hostPath:
                      path: "/var/run/docker.sock"
                  - name: docker-directory
                    hostPath:
                      path: "/var/lib/docker"
              """.stripIndent()
          }
        }
          
        steps {
          container('golang-build-coverage') {
            withCredentials(bindings: [sshUserPrivateKey(credentialsId: "${BUILD_SSH_KEY_ID}", keyFileVariable: 'SSH_KEY_FOR_GOMOD')]) {            // 绑定ssh key，ID可以从jenkins上获取，不要使用带密码的key

              sh '''

                wget https://lxs3.b.qianxin-inc.cn/darwintest/build/ci/darwin-ci -O /bin/darwin-ci
                chmod +x /bin/darwin-ci
                # 生成私钥并放入默认的位置
                export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o User=git"
                git config --global url."**************************:".insteadOf "https://git-biz.qianxin-inc.cn/"
                mkdir -p  ~/.ssh
                chmod 700 ~/.ssh
                cat ${SSH_KEY_FOR_GOMOD} > ~/.ssh/id_rsa
                chmod 400 ~/.ssh/id_rsa

                if [ "${COMMITID}" != "" ]
                then
                  git checkout "${COMMITID}"
                  echo "Testing checkout commitid!!!"
                fi

                # 生成docker镜像的 tag,建议规则是 "当前日期.分支名_CommitID.BuildID"
                VERSION=${IMAGE_TAG}
                echo "$DOCKER_HARBOR_CREDS_PSW" | docker login -u "$DOCKER_HARBOR_CREDS_USR" "$DOCKER_HARBOR_SERVER" --password-stdin
                rm -rf go.sum
               
                wget https://af-biz.qianxin-inc.cn:443/artifactory/test_release/goc 
                
                chmod +x goc
                
                if [ -f "go.mod" ]; then
                  go mod tidy
                fi 
                # 程序构建指令
                darwin-ci  -docker_warehouse_path=${DOCKER_REPOSITORY}   -binary_warehouse_path=${GENERIC_REPOSITORY}   -build_id=${BUILD_ID}  -build_type=build -test_type=test -execute_type=build_code_test -test_args=${TESTING_CENTER}  -git_addr=${GIT_REMOTE_URL} -branch=${BRANCH_NAME} -commit_id=${commitid} -git_commit_log=${GITCOMMITLOG}   -job_name=${PROJECT_SERVICE_NAME} ${LINUX_DARWIN_ARGS}

                # 镜像上传指令
                echo 'ArkServerAddr = "https://ark.ops.qianxin-inc.cn:443"' > $HOME/.ark/config.toml
                (cd arkbuild && ark-cli -f ark.yaml build -t ${VERSION}.codetest -r "${DOCKER_HARBOR_SERVER}" -p)

              '''

            }
          }
        }
      }

    // Build app for windows version
    stage('Build app On Windows') {
      when {
        beforeAgent true
        expression { WINDOWS_BUILD == 'true'  &&  params.IMAGE_FROM ==  '' && params.DOCKER_IMAGE ==  ''    }
      }
      agent {
        kubernetes {
          yaml """\
            apiVersion: v1
            kind: Pod
            metadata:
              labels:
                jenkins-job: mydemo
            spec:
              containers:
              - name: jnlp
                image: 'qianxin-image-local.af-biz.qianxin-inc.cn/qax-cicd/jenkins/inbound-agent:jdk17-windowsservercore-ltsc2019'
                tty: true
              - name: winamd64-golang-build
                image: 'qianxin-image-local.af-biz.qianxin-inc.cn/qax-cicd/winamd64/golang:1.24.3-20250522'
                tty: true
                volumeMounts:
                  - name: golang-data
                    mountPath: c:/gopath
              volumes:
              - name: golang-data
                hostPath:
                  path: D:/data/golang
                  type: DirectoryOrCreate
              nodeSelector:
                BuildNodeArch: windows
            """.stripIndent()
        }
      }

      steps {
            container('winamd64-golang-build'){
              withCredentials(bindings: [sshUserPrivateKey(credentialsId: "${BUILD_SSH_KEY_ID}", keyFileVariable: 'SSH_KEY_FOR_GOMOD')]) {
                bat """
                 echo %PATH%  =================
                          echo %SSH_KEY_FOR_GOMOD% > testfile
                          mkdir %USERPROFILE%\\.ssh
                          git config --global url."**************************:".insteadOf "https://git-biz.qianxin-inc.cn/"
                          powershell (copy %SSH_KEY_FOR_GOMOD% %USERPROFILE%\\.ssh\\id_rsa)
                          ssh-keyscan git-biz.qianxin-inc.cn >> %USERPROFILE%\\.ssh\\known_hosts
                          set SIGTOOLS=signtool.exe
                          if exist %SIGTOOLS% (
                              del %SIGTOOLS%
                          )
                          el %SIGTOOLS%
                      )
                      
                      powershell (new-object System.Net.WebClient).DownloadFile('https://af-biz.qianxin-inc.cn:443/artifactory/test_release/signtool.exe','%SIGTOOLS%')
                      set CIFILENAME=darwin-ci.exe
                      if exist %CIFILENAME% (
                          del %CIFILENAME%
                      )
                       powershell (new-object System.Net.WebClient).DownloadFile('https://lxs3.b.qianxin-inc.cn/darwintest/build/ci/darwin-ci.exe?id=%BUILD_ID%','%CIFILENAME%')
                    #   powershell (new-object System.Net.WebClient).DownloadFile('https://minio.ops.qianxin-inc.cn/darwincloudqatest/hjh/build/ci/darwin-ci.exe?id=%BUILD_ID%','%CIFILENAME%')
                      set GOVERSIONINFO=goversioninfo.exe
                      set GOPRIVATE=git-biz.qianxin-inc.cn,git-open.qianxin-inc.cn
                      set GOFLAGS=-mod=mod 
                      powershell (new-object System.Net.WebClient).DownloadFile('https://af-biz.qianxin-inc.cn:443/artifactory/test_release/goversioninfo.exe','%GOVERSIONINFO%')
                      set SKYLARICO=skylar.ico
                      powershell (new-object System.Net.WebClient).DownloadFile('https://af-biz.qianxin-inc.cn:443/artifactory/test_release/skylar.ico','%SKYLARICO%')
                      set commit_id="%CI_COMMIT_SHORT_SHA%"
                      if  "%COMMITID%" NEQ "" (
                        echo "need rebuild!--windows"
                        commit_id="%COMMITID%"
                        git checkout "%COMMITID%"
                      ) else (
                        commit_id=%CI_COMMIT_SHORT_SHA%
                        echo "commit_id:%CI_COMMIT_SHORT_SHA%"
                      )
                      del go.sum
                      darwin-ci.exe   -binary_warehouse_path=%GENERIC_REPOSITORY%   -branch=%BRANCH_NAME% -prs_params=%PRS_PARAMS%  -git_commit_log=%GITCOMMITLOG% -framework=%FRAMEWORK% -job_name=%PROJECT_SERVICE_NAME% -build_id=%BUILD_ID% -commit_id=%commit_id% -is_export_prs=%IS_EXPORT_PRS% -execute_type=windows -tag_version=0.0.1 -git_addr=%GIT_REMOTE_URL% %WINDOWS_DARWIN_ARGS%                      
                        
                """
              }
            }
          }
        }

        stage('Build app On ARM64') {
          environment {
            GOPATH = "/data/golang"
            GOFLAGS = "-mod=mod" 
          }
          when {
            beforeAgent true
            expression { ARM_BUILD == 'true'  &&  params.IMAGE_FROM ==  '' && params.DOCKER_IMAGE ==  ''   }
          }
          agent {
            kubernetes {
              yaml """\
                apiVersion: v1
                kind: Pod
                metadata:
                  labels:
                    jenkins-job: mydemo
                spec:
                  containers:
                  - name: arm64-golang-build
                    image: 'qianxin-image-local.af-biz.qianxin-inc.cn/qax-cicd/jenkins-slave-golang-dind:centos8.3-arm64v8-v1.24.3-20250522'
                    command:
                    - cat
                    tty: true
                    volumeMounts:
                      - mountPath: /var/run/docker.sock
                        name: docker-sock
                        readOnly: false
                      - mountPath: "/var/lib/docker"
                        name: docker-directory
                        readOnly: false
                      - mountPath: "/data/golang"
                        name: golang-data
                  nodeSelector:
                    BuildNodeArch: arm64
                  tolerations:
                  - key: "BuildNodeArch"
                    operator: "Exists"
                    effect: "NoSchedule"
                  volumes:
                    - name: golang-data
                      hostPath:
                        path: /data/jenkins-slave-data/golang
                        type: Directory
                    - name: docker-sock
                      hostPath:
                        path: "/var/run/docker.sock"
                    - name: docker-directory
                      hostPath:
                        path: "/var/lib/docker"
              """.stripIndent()
            }
          }
          steps {
            container('arm64-golang-build') {
              withCredentials(bindings: [sshUserPrivateKey(credentialsId: "${BUILD_SSH_KEY_ID}", keyFileVariable: 'SSH_KEY_FOR_GOMOD')]) {
                sh '''
                  source /root/.cargo/env 
                  export LD_LIBRARY_PATH="/usr/lib"
                  export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o User=git"
                  git config --global url."**************************:".insteadOf "https://git-biz.qianxin-inc.cn/"
                  mkdir ~/.ssh
                  chmod 700 ~/.ssh
                  cat ${SSH_KEY_FOR_GOMOD} > ~/.ssh/id_rsa
                  chmod 400 ~/.ssh/id_rsa
                  wget https://lxs3.b.qianxin-inc.cn/darwintest/build/ci/darwin-arm64-ci?id=${BUILD_ID} -O darwin-ci
                  chmod +x darwin-ci
                  VERSION=${DATE}.${GIT_BRANCH_SLUG}_${CI_COMMIT_SHORT_SHA}.${BUILD_ID}_arm64

                  commitid="${COMMITID}"

                  if [[ "${COMMITID}" != "" ]]
                  then
                    git checkout "${COMMITID}"
                    echo "Testing checkout commitid!!!"
                    VERSION=${DATE}.${GIT_BRANCH_SLUG}_${COMMITID}.${BUILD_ID}_arm64
                    echo "image VERSION ${VERSION}"
                  else
                    commitid=${CI_COMMIT_SHORT_SHA}
                  fi
                   rm -rf go.sum
                  echo "$DOCKER_HARBOR_CREDS_PSW" | docker login -u "$DOCKER_HARBOR_CREDS_USR" "$DOCKER_HARBOR_SERVER" --password-stdin
                  # 程序构建指令 (Custom)

                  # 镜像上传指令 (Custom)
                  echo 'ArkServerAddr = "https://ark.ops.qianxin-inc.cn:443"' > $HOME/.ark/config.toml

                  export npm_config_disturl=https://af-biz.qianxin-inc.cn/artifactory/test_release/nodejs
                  
                  ./darwin-ci   -docker_warehouse_path=${DOCKER_REPOSITORY}   -binary_warehouse_path=${GENERIC_REPOSITORY}    -build_id=${BUILD_ID} -prs_params=${PRS_PARAMS}  -docker_harbor=${DOCKER_HARBOR_SERVER} -docker_version=${ARM_IMAGE_TAG} -execute_type=linux_arm64 -job_name=${PROJECT_SERVICE_NAME} -is_export_prs=${IS_EXPORT_PRS} -branch=${BRANCH_NAME} -commit_id=${commitid} -git_commit_log=${GITCOMMITLOG} -git_addr=${GIT_REMOTE_URL} $LINUX_ARM_DARWIN_ARGS
                  
                '''
              }
            }
          }
        }
       stage('Build app On MIPS') {
          environment {
              GOPATH = "/data03/go"
              GOFLAGS = "-mod=mod" 
            }
            when {
              beforeAgent true
              expression { MIPS_BUILD == 'true' &&  params.IMAGE_FROM ==  '' && params.DOCKER_IMAGE ==  ''   } 
            }
            agent {
                label "mips"
            }
            stages('MIPS build and clean dir ') {
              stage('MIPS build '){
	              steps {
	                withCredentials(bindings: [sshUserPrivateKey(credentialsId: "${BUILD_SSH_KEY_ID}", keyFileVariable: 'SSH_KEY_FOR_GOMOD')]) {
	                  sh '''
                        
	                    export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o User=git"
	                    git config --global url."**************************:".insteadOf "https://git-biz.qianxin-inc.cn/"
	                   # mkdir ~/.ssh
	                   # chmod 700 ~/.ssh
	                   # cat ${SSH_KEY_FOR_GOMOD} > ~/.ssh/id_rsa
	                   # chmod 400 ~/.ssh/id_rsa
                     
                     MIPS_VERSION=${DATE}.${GIT_BRANCH_SLUG}_${CI_COMMIT_SHORT_SHA}.${BUILD_ID}_MIPS64
                     commitid="${COMMITID}"

                      if [[ "${COMMITID}" != "" ]]
                      then
                        git checkout "${COMMITID}"
                        echo "Testing checkout commitid!!!"
                        MIPS_VERSION=${DATE}.${GIT_BRANCH_SLUG}_${COMMITID}.${BUILD_ID}_MIPS64
                        echo "MIPS_VERSION: ${MIPS_VERSION}"
                      else
                        commitid=${CI_COMMIT_SHORT_SHA}
                      fi

	                    echo "$DOCKER_HARBOR_CREDS_PSW" | docker login -u "$DOCKER_HARBOR_CREDS_USR" "$DOCKER_HARBOR_SERVER" --password-stdin
                      # git checkout  $MIPS_COMMIT

	                    # 镜像上传指令 (Custom)
	                    echo 'ArkServerAddr = "https://ark.ops.qianxin-inc.cn:443"' > $HOME/.ark/config.toml

	                    unset http_proxy
	                    unset https_proxy
                      rm -rf go.sum
	                    darwin-mips-ci  -docker_warehouse_path=${DOCKER_REPOSITORY}   -binary_warehouse_path=${GENERIC_REPOSITORY}    -build_id=${BUILD_ID} -prs_params=${PRS_PARAMS} -git_commit_log=${GITCOMMITLOG} -docker_harbor=${DOCKER_HARBOR_SERVER} -docker_version=${MIPS_IMAGE_TAG} -execute_type=linux_mips64 -job_name=${PROJECT_SERVICE_NAME} -is_export_prs=${IS_EXPORT_PRS} -branch=${BRANCH_NAME} -commit_id=${commitid} -git_addr=${GIT_REMOTE_URL} $LINUX_MIPS_DARWIN_ARGS                     
                     
	                  '''
	                }
                }
            }
          }
        }

      stage('Build app On LOONG') {
            environment {
              GOPATH = "/root/go"
              GOFLAGS = "-mod=mod" 
            }
            when {
                beforeAgent true
                expression {LOONG_BUILD == 'true' &&  params.IMAGE_FROM ==  '' && params.DOCKER_IMAGE ==  ''  } 
                environment name: 'ISBINARY', value: 'false'
            }
            agent {
                label "loongarch64"
            }
            stages('LOONG build ') {
                stage('LOONG build '){
	            steps {
	                withCredentials(bindings: [sshUserPrivateKey(credentialsId: "${BUILD_SSH_KEY_ID}", keyFileVariable: 'SSH_KEY_FOR_GOMOD')]) {
	                  sh '''
	                    export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o User=git"
	                    git config --global url."**************************:".insteadOf "https://git-biz.qianxin-inc.cn/"
	                   # mkdir ~/.ssh
	                   # chmod 700 ~/.ssh
	                   # cat ${SSH_KEY_FOR_GOMOD} > ~/.ssh/id_rsa
	                   # chmod 400 ~/.ssh/id_rsa

                     LOONG_VERSION=${DATE}.${GIT_BRANCH_SLUG}_${CI_COMMIT_SHORT_SHA}.${BUILD_ID}_LOONG64
                     commitid="${COMMITID}"

                      if [[ "${COMMITID}" != "" ]]
                      then
                        git checkout "${COMMITID}"
                        echo "Testing checkout commitid!!!"
                        LOONG_VERSION=${DATE}.${GIT_BRANCH_SLUG}_${COMMITID}.${BUILD_ID}_LOONG64
                        echo "LOONG_VERSION: ${LOONG_VERSION}"
                      else
                        commitid=${CI_COMMIT_SHORT_SHA}
                      fi


	                    echo "$DOCKER_HARBOR_CREDS_PSW" | docker login -u "$DOCKER_HARBOR_CREDS_USR" "$DOCKER_HARBOR_SERVER" --password-stdin
                      rm -rf go.sum

	                    # 镜像上传指令 (Custom)
	                    echo 'ArkServerAddr = "https://ark.ops.qianxin-inc.cn:443"' > $HOME/.ark/config.toml
	                     
                      darwin-loong64-ci -docker_warehouse_path=${DOCKER_REPOSITORY}   -binary_warehouse_path=${GENERIC_REPOSITORY}    -build_id=${BUILD_ID} -prs_params=${PRS_PARAMS} -git_commit_log=${GITCOMMITLOG} -docker_harbor=${DOCKER_HARBOR_SERVER} -docker_version=${LOONG_IMAGE_TAG} -execute_type=linux_loong64  -job_name=${PROJECT_SERVICE_NAME} -is_export_prs=${IS_EXPORT_PRS} -branch=${BRANCH_NAME} -commit_id=${commitid} -git_addr=${GIT_REMOTE_URL} $LINUX_LOONG_DARWIN_ARGS

	                  '''
	                }
                }
               }

            }
        }

  stage('Build X86kylin system ') {
           environment {
             GOFLAGS = "-mod=mod" 
             GOPATH = "/data/golang"
           }
        when {
         beforeAgent true
            expression { params.IMAGE_FROM != '' || params.DOCKER_IMAGE != '' }   
        } 
        
         agent {     // 定义使用的节点
           kubernetes {  // 定义使用kubernetes 动态创建slave 节点，slave节点可以支持linux、windows
           // defaultContainer 'golang-build'    // 定义执行shell的默认的container，假设POD的yaml中定义了多个container
             yaml """\
               apiVersion: v1
               kind: Pod
               metadata:
                 labels:
                   jenkins-job: mydemo
               spec:
                 securityContext:
                   runAsUser: 0
                 containers:
                 - name: golang-build-kylin
                   image: 'qianxin-image-local.af-biz.qianxin-inc.cn/qax-cicd/jenkins-slave-golang-dind:kylin-v10-v1.19.3-20221205'
                   command:
                   - cat
                   tty: true
                   volumeMounts:
                     - mountPath: /var/run/docker.sock
                       name: docker-sock
                       readOnly: false
                     - mountPath: "/var/lib/docker"
                       name: docker-directory
                       readOnly: false
                     - mountPath: "/data/golang"
                       name: golang-data
                 volumes:
                   - name: golang-data
                     persistentVolumeClaim:
                       claimName: golang
                   - name: docker-sock
                     hostPath:
                       path: "/var/run/docker.sock"
                   - name: docker-directory
                     hostPath:
                       path: "/var/lib/docker"
               """.stripIndent()
           }
         }
          
         steps {
           container('golang-build-kylin') {
             withCredentials(bindings: [sshUserPrivateKey(credentialsId: "${BUILD_SSH_KEY_ID}", keyFileVariable: 'SSH_KEY_FOR_GOMOD')]) {            // 绑定ssh key，ID可以从jenkins上获取，不要使用带密码的key

               sh '''
                         # 生成私钥并放入默认的位置
                         export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o User=git"
                         git config --global url."**************************:".insteadOf "https://git-biz.qianxin-inc.cn/"
                         mkdir -p  ~/.ssh
                         chmod 700 ~/.ssh
                         cat ${SSH_KEY_FOR_GOMOD} > ~/.ssh/id_rsa
                         chmod 400 ~/.ssh/id_rsa
                        wget https://lxs3.b.qianxin-inc.cn/darwintest/build/ci/darwin-ci?id=${BUILD_ID} -O /bin/darwin-ci
                          chmod +x  /bin/darwin-ci

                          commitid="${COMMITID}"

                       if [[ "${COMMITID}" != "" ]]
                       then
                         git checkout "${COMMITID}"
                         echo "Testing checkout commitid!!!"
                       else
                         commitid=${CI_COMMIT_SHORT_SHA}
                       fi
                             rm -rf go.sum
                           # 生成docker镜像的 tag,建议规则是 "当前日期.分支名_CommitID.BuildID"
                         VERSION=${IMAGE_TAG}
                         echo "$DOCKER_HARBOR_CREDS_PSW" | docker login -u "$DOCKER_HARBOR_CREDS_USR" "$DOCKER_HARBOR_SERVER" --password-stdin
                        

                         # 镜像上传指令
                         echo 'ArkServerAddr = "https://ark.ops.qianxin-inc.cn:443"' > $HOME/.ark/config.toml

                         darwin-ci  -image_from=${IMAGE_FROM} -docker_image=${DOCKER_IMAGE}   -docker_warehouse_path=${DOCKER_REPOSITORY}   -binary_warehouse_path=${GENERIC_REPOSITORY}   -build_id=${BUILD_ID} -prs_params=${PRS_PARAMS} -docker_harbor=${DOCKER_HARBOR_SERVER} -docker_version=${VERSION} -execute_type=linux -job_name=${PROJECT_SERVICE_NAME}  -git_addr=${GIT_REMOTE_URL} -branch=${BRANCH_NAME} -commit_id=${commitid} -git_commit_log=${GITCOMMITLOG} -is_export_prs=${IS_EXPORT_PRS} $LINUX_DARWIN_ARGS
            

               '''

             }
           }
         }
       }

      }
    }
    stage('Build downstrem app') {
      when { expression { env.DOWNSTREAM_REPOS != null } }
      steps {
        script {
          echo "Build downstream job from ${env.BRANCH_NAME}"
          def downstreamRepos = env.DOWNSTREAM_REPOS.split('[;, ]')
          for (downstreamRepo in downstreamRepos ) {
            def downstreamJob = gitRepoToJenkinsJob(downstreamRepo)
            echo "build downstream repo: ${downstreamJob}"
            build job: downstreamJob, wait: false
          }
        }
      }
    }

    // Deploy Stage
    stage ('Deploy to DEV') {
      when { expression { env.BRANCH_NAME =~ env.DEV_DEPLOY_BRANCH_PATTERN } }
      steps{
        container('golang-build') {           // 执行下面shell命令时的运行环境，此处是golang 语言的构建环境
          sh '''
            TASK_BEGEN_TIME=$((`date +%s%N` / 1000000))

            # 获取Tag
            VERSION=${IMAGE_TAG}

            # 开始部署，通过ark平台的接口，将服务部署到k8s上 (Custom)
            curl --location --request POST 'https://ark.ops.qianxin-inc.cn/api/v1/instance/autotest/imageupdate' \
            --header 'Cookie: other=VGhpc19pc19vdGhlcl9zZXJ2ZXIhQCM2' \
            --header 'Content-Type: application/json' \
            --data-raw '{
            "Image": "'${DOCKER_HARBOR_SERVER}'/'${PROJECT_NAME}'/'${PROJECT_SERVICE_NAME}':'${VERSION}'",
            "instance_name": "'${PROJECT_SERVICE_NAME}'",
            "env_name": "'${DEPLOY_TO_DEV_NAME}'",
            "project_name": "'${DEPLOY_TO_DEV_PROJECT_NAME}'"
            }'
            '''
        }
      }
    }
      
   // golang unit test to sonar
        stage('unit test to sonar  ') {
          environment {
            GOFLAGS = "-mod=mod" 
            GOPATH = "/data/golang"
            PATH = "/bin:/usr/local/go/bin:/usr/local/openjdk-8/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/local/sonar/bin"
          }
         
          steps {
            catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
            container('golang-build') {
              withCredentials(bindings: [sshUserPrivateKey(credentialsId: "${BUILD_SSH_KEY_ID}", keyFileVariable: 'SSH_KEY_FOR_GOMOD')]) {            // 绑定ssh key，ID可以从jenkins上获取，不要使用带密码的key
              
               sh '''
                         if [[ "${GO_TEST_COMMAND}" != "" ]]
                      then
                         set +e
                         # 生成私钥并放入默认的位置
                        export LANG=en_US.UTF-8
                        TASK_BEGEN_TIME=$((`date +%s%N` / 1000000))
                        export GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no -o User=git"
                        git config --global url."**************************:".insteadOf "https://git-biz.qianxin-inc.cn/"
                        mkdir -p  ~/.ssh
                        chmod 700 ~/.ssh
                        cat ${SSH_KEY_FOR_GOMOD} > ~/.ssh/id_rsa
                        chmod 400 ~/.ssh/id_rsa
                        # 将 go list 命令的输出赋值给变量 dirs
                        dirs=$(go list -f '{{if not .TestGoFiles}}{{.ImportPath}}{{end}}' ./...)
                        
                        # 打印变量 dirs 的内容
                        echo "$dirs"

                        if [ "${COMMITID}" != "" ]
                        then
                          git checkout "${COMMITID}"
                          echo "Testing checkout commitid!!!"
                        fi
                        eval "$GO_TEST_COMMAND"  | tee test.out
                        

                        go tool cover -func=./allcover.out -o ./allcover.txt
                        # 将 go list 命令的输出赋值给变量 dirs
                        dirs=$(go list -f '{{if not .TestGoFiles}}{{.ImportPath}}{{end}}' ./...)
                        
                        # 打印变量 dirs 的内容
                        echo "$dirs"
                        
                        exclusions=""
                        for dir in $dirs; do
                          echo "Processing directory: $dir"
                          # 在这里添加您的具体操作逻辑
                          relative_path=${dir#*.git/}
                          exclusions+=",/$relative_path/**"
                          echo "exclusions==$exclusions"
                        done
                        
                        go test -coverprofile=coverage.out ./...  
                 
                        go tool cover -html=coverage.out
                        cat coverage.out
                        wget https://af-biz.qianxin-inc.cn:443/artifactory/test_release/unittest_tools/GoUnitTest.sh
                        
                        chmod +x GoUnitTest.sh
                        
                        GIT_URL=`echo $GIT_REMOTE_HTTPS | sed -e 's/.git$//g'`
                       
                       
                       CI_PROJECT_URL=${GIT_URL}

                        CI_COMMIT_REF_NAME=${GIT_BRANCH_SLUG}

curl --noproxy '*' -X POST -d "repository=${CI_PROJECT_URL}&branch=${GIT_BRANCH}&commitId=${CI_COMMIT_SHA}&commitUser=${CI_COMMIT_EMAIL}" "https://fusion.qianxin-inc.cn/pipeline/track/pipelines/data/pre-sonar-info-handle"
sonar_info=$(curl --noproxy '*' -sS 'https://fusion.qianxin-inc.cn/pipeline/track/pipelines/data/sonar-info?repository='${CI_PROJECT_URL}'&branch='${GIT_BRANCH})
echo $sonar_info
eval $(echo "$sonar_info" | awk -F ' ' '{printf("sonar_host=%s;project_key=%s;sonar_token=%s",$1,$2,$3)}')
curl -u ${sonar_token}: --request POST --data "project=${project_key}&name=${project_key}" "${sonar_host}/api/projects/create" || true
#获取指定仓库分支的排除命令
exclusion_info=$(curl --noproxy '*' -sS -X GET "https://fusion.qianxin-inc.cn/pipeline/data/api/v1/citools/sonarexclutions?repo=${CI_PROJECT_URL}&branch=${GIT_BRANCH}")
echo ${exclusion_info}   
sonar-scanner -Dsonar.host.url=$sonar_host -Dsonar.pdf.skip=true   -Dsonar.ws.timeout=300000 -Dsonar.exclusions=**/*.yaml$exclusions,**/*.py,**/*_test.go  -Dsonar.projectKey=$project_key -Dsonar.go.coverage.reportPaths=coverage.out  -Dsonar.login=$sonar_token

                      fi
                      
test -e .scannerwork/report-task.txt && source .scannerwork/report-task.txt || echo 'sonar analysis error'
if [ -n "$ceTaskId" ]; then
        param='{"repository":"'$CI_PROJECT_URL'","branch":"'${GIT_BRANCH}'","taskUuid":"'$ceTaskId'","commitSha":"'${GIT_COMMIT}'","sonarHost":"'$serverUrl'"}'
        curl --noproxy '*' -sS -X POST \
              -H "Content-Type: application/json" \
              -d "$param" "https://fusion.qianxin-inc.cn/pipeline/track/pipelines/data/sonar-task"
    else
      #调用上报单测覆盖率统计失败的接口上报
      echo 'java-unit-test failed'
     #  param='{"repository":"'$CI_PROJECT_URL'","branch":"'${GIT_BRANCH}'","commitSha":"'${GIT_COMMIT}'","commitEmail":"'${GIT_COMMITTER_EMAIL}'","jobUrl":"'${BUILD_URL}'","commitTimestamp":"'${TASK_BEGEN_TIME}'"}'
         # curl --noproxy '*' -sS -X POST \
          #      -H "Content-Type: application/json" -k \
           #     -d "$param" "https://fusion.qianxin-inc.cn/pipeline/track/pipelines/data/unit-test-error"
fi

                     '''
            
                }
              }
            }
          }
        }      
      //Code QL sacnner

      stage ('CodeQL Scanner') {
            environment {
            GOFLAGS = "-mod=mod" 
            GOPATH = "/data/golang"
            PATH = "/bin:/usr/local/go/bin:/usr/local/openjdk-8/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/local/sonar/bin"
               }
        steps{
           catchError(buildResult: 'SUCCESS', stageResult: 'FAILURE') {
        container('golang-build') {           // 执行下面shell命令时的运行环境，此处是golang 语言的构建环境
        timeout(time: 60, unit: 'MINUTES') {
          sh '''
           
           wget https://af-biz.qianxin-inc.cn:443/artifactory/autoscan-local-generic/codeqlscan.tar.gz
           
           tar  -C /tmp/  -zxvf codeqlscan.tar.gz 
           
           echo $CODEQL_COMMAND
            response=$(curl -k --location 'https://dtc.qianxin-inc.cn/e3-ci-controller/psm/git_need_scan' \
                     --header 'X-DTC-Token: 2jzGOFSmuLnpciIi2Yq6SVmO' \
                     --form 'type="SAST"' \
                     --form 'git_ssh_url="'${GIT_REMOTE_URL}'"' \
                     --form 'branch="'${GIT_BRANCH}'"')

        results=$(echo "$response" | jq -r '.results')

    if [[ "$results" == "true" ]]; then
    if /tmp/codeql/codeqlscan -language='go'  -command="${CODEQL_COMMAND}" 
    
     ISVULFLE=$(find ./vulfile.tar.gz | sed 's#.*/##')
           export GIT_COMMIT_AUTHOR=`git show -s --format=%an`
           echo $ISVULFLE
            
          if [[ ${ISVULFLE} != "" ]]; then
                       
                       curl --location 'https://dtc.qianxin-inc.cn/gateway/receive/notice-codeql' --header 'X-DTC-Token: 2jzGOFSmuLnpciIi2Yq6SVmO' --form   'info={"name":"api_assets_rule","git_ssh_url":"'${GIT_REMOTE_URL}'","branch":"'${BRANCH_NAME}'", "user_email":"'${GIT_COMMITTER_EMAIL}'","user_username":"'${GIT_COMMIT_AUTHOR}'", "language":"go","devops":1}' --form 'vulfile=@"./vulfile.tar.gz"' --form 'result=@"./result.sarif"'
                     
                      else

                      curl --location 'https://dtc.qianxin-inc.cn/gateway/receive/notice-codeql' --header 'X-DTC-Token: 2jzGOFSmuLnpciIi2Yq6SVmO' --form 'info={"name":"api_assets_rule","git_ssh_url":"'${GIT_REMOTE_URL}'","branch":"'${BRANCH_NAME}'", "user_email":"'${GIT_COMMITTER_EMAIL}'","user_username":"'${GIT_COMMIT_AUTHOR}'", "language":"go","devops":1}'  --form 'result=@"./result.sarif"'

                      fi ; then
        echo "codeql scan successfully."
         else
        curl -k --location 'https://dtc.qianxin-inc.cn/gateway/receive/notice-codeql-scanresult' \
            --header 'X-DTC-Token: 2jzGOFSmuLnpciIi2Yq6SVmO' \
            --header 'Content-Type: application/json' \
            --data-raw '{
                "jenkins-url": "'${BUILD_URL}'",
                "git-url": "'${GIT_REMOTE_URL}'",
                "branch": "'${GIT_BRANCH}'"
            }'
    fi
fi
           
            '''
        }
        }
      }
    }
      }


  }

  // TODO: notify
  post {
    success {
      echo "This will run only if successful"
    }
    failure {
     script {
         USER_EMAIL = sh(
             script: "git log -1 |grep Author| awk '{print \$3}' | sed 's/<//; s/>//'",
             returnStdout: true
         ).trim()
     }
     mail to: "${USER_EMAIL},${RECEIVE_EMAIL_LIST}", from: '<EMAIL>',
         subject: "Jenkins CICD Build: ${env.JOB_NAME} - 失败",
         body: "Job Failed - \"${env.JOB_NAME}\" build: ${env.BUILD_NUMBER}\n\nView the log at:\n ${env.RUN_DISPLAY_URL}"
    }
  }
}
