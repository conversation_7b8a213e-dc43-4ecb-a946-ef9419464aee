package av_v1

import (
	// gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	// impl "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/impl"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
)

func StartServer(sp server.ServiceProvider) error {
	return Server.Start(sp)
}

func StopServer(sp server.ServiceProvider) {
	Server.Stop()
}
