package av_v1 

import (
	//"context"
	//"errors"
	"sync"
	sorm "git-biz.qianxin-inc.cn/ats-rd2-infra-components/sdk/sorm.git/orm"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
	"git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/av_v1"
	impl "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/impl"
	v1_server "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/av_v1/server"
	service "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/pkg/av_v1/service"
	model "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/model"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
)

var initOnce sync.Once
var Server *AntiVirusServiceV1

func init() {
	initOnce.Do(func() {
		Server = &AntiVirusServiceV1{}
	})
	sorm.RegisterModel(new(model.AntiRansom))
	sorm.RegisterModel(new(model.HealthCheck))
	//注册所有的model和注册server服务
	v1_server.RegisterService(Server,
		server.WithServiceStartHook(StartServer),
		server.WithServiceStopHook(StopServer))
}

type AntiVirusServiceV1 struct {
	server.ServiceProvider
	*service.AntiRansom	
	*service.HealthCheck
}

var _ av_v1.AvV1MustServer  =  &AntiVirusServiceV1{}

func (a *AntiVirusServiceV1) Start(sp server.ServiceProvider) error {
	arImpl := impl.NewAntiRansomImpl(sp)
	if arImpl == nil {
		gflog.Errorf("NewAntiRansomImpl return nil")
	}
	
	hcImpl := impl.NewHealthCheckImpl(sp)
	if hcImpl == nil {
		gflog.Errorf("NewHealthCheckImpl return nil")
	}


	a.AntiRansom = service.NewAntiRansom(arImpl)
	a.HealthCheck = service.NewHealthCheck(hcImpl)
	return nil
}

func (a *AntiVirusServiceV1) Stop() error {
	return nil
}
