package impl 

import (
	"time"
	"context"
	"sync/atomic"
	"github.com/robfig/cron/v3"
	"github.com/golang/protobuf/ptypes/timestamp"
	"google.golang.org/grpc/codes"
	status "google.golang.org/genproto/googleapis/rpc/status"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
	//gen_type "git-biz.qianxin-inc.cn/zion-infra/styx/styx-protocol.git/generated-go/zion/styx/gen_type"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	"git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	model "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/model"
	jarvis "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/jarvis"
)

type PerformanceStrategyJobChecker struct {
	stopCh 		chan int
	quickCh 	chan int 
	CheckName 	string
	CheckId 	av.HealthCheckItem 
	cJob        *cron.Cron
	processFlag int32 //为了避免并发的多次检查
	hc          model.HealthCheckModel
}


func NewPerformanceStrategyJobChecker(sp server.ServiceProvider, firstCheck bool) JobChecker {
	
	hc := CreateHealthCheckModel(int32(av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER), sp)
	if hc == nil {
		gflog.Errorf("CreateHealthCheckModel failed")
		return nil
	}

	checker := &PerformanceStrategyJobChecker{
		CheckName: "病毒防护策略-影响终端性能的配置项检查",
		CheckId: av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER,
		quickCh: make(chan int),
		stopCh: make(chan int),
		processFlag: IDLE_JOB,
		hc: hc,
	}

	c := cron.New(cron.WithSeconds())
	_, err := c.AddFunc("@midnight", checker.check)
    if err != nil {
        gflog.Errorf("add check to cron job failed err=%v", err)
        return nil	
    }
	c.Start()
	checker.cJob = c

	go checker.checkLoop()
	//半小时后检查一次
	if firstCheck {
		go func() {
			time.Sleep(1800 * time.Second)
			checker.check()
		}()
	}
	return checker
}

func (c *PerformanceStrategyJobChecker) checkLoop() {
	gflog.Debugf("PerformanceStrategyJobChecker checkLoop start ...")
	defer c.cJob.Stop() //退出之后停掉定时器
	defer atomic.StoreInt32(&c.processFlag, DEAD_JOB)
	//job只要初始化，就立即检查一次。
	//1. 开关重新开启 2. 服务重新启动 3. 服务第一次安装运行
	c.check()
	for {
		select {
		case <-c.stopCh:
			gflog.Infof("PerformanceStrategyJobChecker is shut down...")
			return 
		case <-c.quickCh:
			c.check()
		}
	}
}

func (c *PerformanceStrategyJobChecker) check() {

	if !atomic.CompareAndSwapInt32(&c.processFlag, IDLE_JOB, PORCESSING_JOB) {
		gflog.Debugf("PerformanceStrategyJobChecker is checking ...")
		return
	}
	defer atomic.StoreInt32(&c.processFlag, IDLE_JOB)
	
	gflog.Debugf("PerformanceStrategyJobChecker check  ...")
	
	//1. 检查策略项
	suspectPolicies := []*av.PolicyCheckResult{}
	
	pUtils := GetPolicyUtils()
	policies, _, err := pUtils.GetAntiVirusPoliciesPartially(context.Background(), 20)
	if err != nil {
		//遇到错误直接退出
		gflog.Errorf("GetAntiVirusPoliciesPartially failed err=%v", err)
		return 
	}

	for i := 0; i < len(policies); i++ {
		policy := policies[i]
		for j := 0; j < len(policy.GetObjects()); j++ {
			object := policy.Objects[j]
			if object.GetKey().GetItemName() == "defense_operation_type.read.enabled" { 
				if object.GetValue().GetBoolScalar().GetValue() == true {
					policy.Objects = []*jarvis.MapObjectKeyObjectValueEntry{object}
					suspectPolicies = append(suspectPolicies, policy)
				}
			}
		}
	}
	
	//只要出现这种策略项就需要提醒
	var result *av.HealthCheckResult
	if len(suspectPolicies) > 0 {
		result = &av.HealthCheckResult{
			CheckId:  av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER,
			ItemName: c.CheckName,
			Policies: suspectPolicies,
			CheckTime: &timestamp.Timestamp{
				Seconds: time.Now().Unix(),
			},
		}	
	}

	//更新到数据库
	c.hc.UpdateResult(int32(av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER), result)
}

func (c *PerformanceStrategyJobChecker) StopJob() {
	c.stopCh <- 1
}

func (c *PerformanceStrategyJobChecker) TriggerACheck() {
	c.quickCh <- 1
}

func (c *PerformanceStrategyJobChecker) GetCheckResult()  (*av.HealthCheckResult, *status.Status)  {
	//查询数据库
	result, err := c.hc.GetAllResult(int32(av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER))
	if err != nil {
		gflog.Errorf("PerformanceStrategyJobChecker GetCheckResult failed err=%v", err)
		return &av.HealthCheckResult{}, &status.Status{Code: int32(codes.FailedPrecondition), Message: err.Error()}
	}
	gflog.Debugf("PerformanceStrategyJobChecker GetCheckResult result=%v", result)
	return result, &status.Status{}
}

func (c *PerformanceStrategyJobChecker) GetJobStatus() int32 {
	return atomic.LoadInt32(&c.processFlag)
}
