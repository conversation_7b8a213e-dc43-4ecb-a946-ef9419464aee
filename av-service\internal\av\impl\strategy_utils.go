package impl

import (
	"context"
	// "math/rand"
	// "time"

	"git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/jarvis_v1"
	jcli "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/jarvis_v1/client"
	jarvis "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/jarvis"
	"google.golang.org/grpc/metadata"

	gfcred "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/credentials"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/dialer"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"

	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
)

var policyUtils PolicyUtilsInterface


type PolicyUtilsInterface interface {
	GetAntiVirusPoliciesPartiallyWithStaffid(ctx context.Context, limit int64) ([]*av.PolicyCheckResult, error)
	GetAntiVirusPoliciesPartially(ctx context.Context, limit int64) ([]*av.PolicyCheckResult, []*jarvis.PolicyMeta, error) 
	CountAntiVirusPolicies(ctx context.Context) (int64, error) 
}

type PolicyUtils struct {
	jClient jarvis_v1.JarvisV1Client
	outClient jarvis_v1.JarvisV1Client //外部client
}

func GetPolicyUtils() PolicyUtilsInterface {
	if policyUtils == nil {
		policyUtils = NewPolicyUtils()
	}
	return policyUtils
}

//初始化策略接口
func NewPolicyUtils() PolicyUtilsInterface {
	jClient, err := jcli.NewJarvisV1ThinClient()
	if err != nil {
		gflog.Errorf("NewJarvisV1ThinClient failed err=%v", err)
		return nil
	}

	dialOptTag := "av_call_jarvis"
	dialer.RegisterDialOption(dialOptTag,
		// 生产环境中，需要加载正式的CA证书，以及在做双向认证时，需要读取客户端的证书和key
		dialer.DialWithCredentialsOption(gfcred.WithInsecureSkipVerify(true),
			gfcred.WithInsecureDial(false)))

	cc := dialer.CreateClientConnection(dialer.WithTarget("trantor.core.JarvisV1"),
		dialer.WithDialOptTag(dialOptTag))
	if cc == nil {
		gflog.Fatalf("create client conn failed, got nil")
	}

	outClient := jarvis_v1.NewJarvisV1Client(cc)
   
	return &PolicyUtils{
		jClient: jClient,
		outClient: outClient,
	}
}

func StringFromContext(ctx context.Context, key string) string {
	md, _ := metadata.FromIncomingContext(ctx)
	if md == nil || len(md[key]) == 0 {
		return ""
	}
	return md[key][0]
}


func (u *PolicyUtils) GetAntiVirusPoliciesPartiallyWithStaffid(ctx context.Context, limit int64) ([]*av.PolicyCheckResult, error) {
	
	policyRequest := &jarvis_v1.GetPoliciesWithExtraFilterPartially_Request{
		Offset: 0,
		Limit: limit,
		//Orders: TODO:看一下这个排序是怎么做的。待调研
		ExtraFilter: &jarvis.PoliciesFilter{
			HasOwner: &jarvis.PoliciesFilter_Owner{
				Owner: &jarvis.PolicyMeta_Owner{
					OwnerType: jarvis.PolicyMeta_Owner_CLIENT,
				},
			},
			HasProject: &jarvis.PoliciesFilter_Project{
				Project: &jarvis.SchemaValue_Project{
					Name: "antivirus",
				},
			},
		},
	}  
	
	newCtx := context.Background()
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		//gflog.Debugf("metadata FromIncomingContext exist=%v md=%v", ok, md)
		for k ,v := range md {
			if len(v) > 0 {
				newCtx = metadata.AppendToOutgoingContext(newCtx, k, v[0])
			}
		}
	}

	if md, ok := metadata.FromOutgoingContext(newCtx); ok {
		gflog.Debugf("metadata FromOutgoingContext exist=%v md=%v", ok, md)
	}

	policies := []*av.PolicyCheckResult{}
	for {
		policyResp, err := u.outClient.GetPoliciesWithExtraFilterPartially(newCtx, policyRequest)
		if err != nil {
			gflog.Errorf("GetPoliciesWithExtraFilterPartially failed err=%v", err)
			return nil, err
		}

		ids := policyResp.GetPolicies() 
		if len(ids) == 0 { //所有的都查出来了，可以停止对策略项的检查
			break
		}

		policyRequest.Offset += int64(len(ids))
		for i := 0; i < len(ids); i++ {
			
			id := ids[i]
			metaResponse, err := u.outClient.GetPolicyMeta(ctx, &jarvis_v1.GetPolicyMeta_Request{
				Key: id,
			})

			if err != nil {
				gflog.Errorf("GetPolicyMeta failed err=%v", err)
				return nil, err
			}

			//只取开启状态的策略项
			if metaResponse.GetMeta().GetState() != jarvis.PolicyMeta_DISABLED {
				objectResponse, err := u.outClient.GetPolicyObjects(ctx, &jarvis_v1.GetPolicyObjects_Request{
					Key: id,
				})

				if err != nil {
					gflog.Errorf("GetPolicyObjects failed err=%v", err)
					return nil, err
				}

				items := make([]*jarvis.MapObjectKeyObjectValueEntry, len(objectResponse.GetObjects()))
				
				for i := 0; i < len(objectResponse.GetObjects()); i++ {
					obj := objectResponse.GetObjects()[i]
					items[i] = &jarvis.MapObjectKeyObjectValueEntry{
						Key: obj.GetKey(),
						Value: obj.GetValue(),
					}
				}

				v, ok := metaResponse.GetMeta().GetExtra()["is_cascade"]
				policies = append(policies,  &av.PolicyCheckResult{
					Key: id,
					PolicyName: metaResponse.GetMeta().GetName(),
					Objects: items,
					IsCascade: ok && v == "true" ,
				})
			}
		}
	}

	return policies, nil
}

func (u *PolicyUtils) GetAntiVirusPoliciesPartially(ctx context.Context, limit int64) ([]*av.PolicyCheckResult, []*jarvis.PolicyMeta, error) {
	policyRequest := &jarvis_v1.GetPoliciesWithExtraFilterPartially_Request{
		Offset: 0,
		Limit: limit,
		//Orders: TODO:看一下这个排序是怎么做的。待调研
		ExtraFilter: &jarvis.PoliciesFilter{
			HasOwner: &jarvis.PoliciesFilter_Owner{
				Owner: &jarvis.PolicyMeta_Owner{
					OwnerType: jarvis.PolicyMeta_Owner_CLIENT,
				},
			},
			HasProject: &jarvis.PoliciesFilter_Project{
				Project: &jarvis.SchemaValue_Project{
					Name: "antivirus",
				},
			},
		},
	}  

	policies := []*av.PolicyCheckResult{}
	metas := []*jarvis.PolicyMeta{}

	for {
		policyResp, err := u.jClient.GetPoliciesWithExtraFilterPartially(ctx, policyRequest)
		if err != nil {
			gflog.Errorf("GetPoliciesWithExtraFilterPartially failed err=%v", err)
			return nil, nil, err
		}
	
		ids := policyResp.GetPolicies() 
		if len(ids) == 0 { //所有的都查出来了，可以停止对策略项的检查
			break
		}

		policyRequest.Offset += int64(len(ids))
	
		for i := 0; i < len(ids); i++ {
			id := ids[i]
			metaRequest := &jarvis_v1.GetPolicyMeta_Request{
				Key: id,
			}
	
			metaResponse, err := u.jClient.GetPolicyMeta(ctx, metaRequest)
			if err != nil {
				gflog.Errorf("GetPolicyMeta failed err=%v", err)
				return nil, nil, err
			}
	
			//只取开启状态的策略项
			if metaResponse.GetMeta().GetState() != jarvis.PolicyMeta_DISABLED {
	
				objectRequest := &jarvis_v1.GetPolicyObjects_Request{
					Key: id,
				}
		
				objectResponse, err := u.jClient.GetPolicyObjects(ctx, objectRequest)
				if err != nil {
					gflog.Errorf("GetPolicyObjects failed err=%v", err)
					return nil, nil, err
				}
	
				m := make(map[string]struct{}, len(objectResponse.Objects))
				items := make([]*jarvis.MapObjectKeyObjectValueEntry, len(objectResponse.GetObjects()))
				for i := 0; i < len(objectResponse.GetObjects()); i++ {
					obj := objectResponse.GetObjects()[i]
					key := obj.GetKey().GetItemName()
					//去重				
					_, ok := m[key]
					if ok {
						continue
					}
					m[key] = struct{}{}
	
					items[i] = &jarvis.MapObjectKeyObjectValueEntry{
						Key: obj.GetKey(),
						Value: obj.GetValue(),
					}
				}
	
				v, ok := metaResponse.GetMeta().GetExtra()["is_cascade"]
				policy := &av.PolicyCheckResult{
					Key: id,
					PolicyName: metaResponse.GetMeta().GetName(),
					Objects: items,
					IsCascade: ok && v == "true" ,
				}
		
				policies = append(policies, policy)
				metas = append(metas, metaResponse.GetMeta())
			}
		}
	}

	return policies, metas, nil
}

func (u PolicyUtils) CountAntiVirusPolicies(ctx context.Context) (int64, error) {
	policyRequest := &jarvis_v1.CountPoliciesWithExtraFilter_Request{
		//Orders: TODO:看一下这个排序是怎么做的。待调研
		ExtraFilter: &jarvis.PoliciesFilter{
			HasOwner: &jarvis.PoliciesFilter_Owner{
				Owner: &jarvis.PolicyMeta_Owner{
					OwnerType: jarvis.PolicyMeta_Owner_CLIENT,
				},
			},
			HasProject: &jarvis.PoliciesFilter_Project{
				Project: &jarvis.SchemaValue_Project{
					Name: "antivirus",
				},
			},
		},
	}

	policyResp, err := u.jClient.CountPoliciesWithExtraFilter(ctx, policyRequest)
	if err != nil {
		gflog.Errorf("CountPoliciesWithExtraFilter failed err=%v", err)
		return -1, err
	}

	return policyResp.GetCount(), nil
}

