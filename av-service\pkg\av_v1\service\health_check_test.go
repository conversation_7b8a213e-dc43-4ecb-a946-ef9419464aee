package service

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	empty "github.com/golang/protobuf/ptypes/empty"
)

// MockHealthCheckService is a mock implementation of HealthCheckService
type MockHealthCheckService struct {
	ctrl     *gomock.Controller
	recorder *MockHealthCheckServiceMockRecorder
}

type MockHealthCheckServiceMockRecorder struct {
	mock *MockHealthCheckService
}

func NewMockHealthCheckService(ctrl *gomock.Controller) *MockHealthCheckService {
	mock := &MockHealthCheckService{ctrl: ctrl}
	mock.recorder = &MockHealthCheckServiceMockRecorder{mock}
	return mock
}

func (m *MockHealthCheckService) EXPECT() *MockHealthCheckServiceMockRecorder {
	return m.recorder
}

func (m *MockHealthCheckService) GetAntivirusHealthCheckResult(ctx context.Context, request *av.GetAntiVirusHealthCheckResult_Request) (*av.GetAntiVirusHealthCheckResult_Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAntivirusHealthCheckResult", ctx, request)
	ret0, _ := ret[0].(*av.GetAntiVirusHealthCheckResult_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockHealthCheckServiceMockRecorder) GetAntivirusHealthCheckResult(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAntivirusHealthCheckResult", reflect.TypeOf((*MockHealthCheckService)(nil).GetAntivirusHealthCheckResult), ctx, request)
}

func (m *MockHealthCheckService) StartAntivirusHealthCheck(ctx context.Context, request *av.StartAntiVirusHealthCheck_Request) (*empty.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartAntivirusHealthCheck", ctx, request)
	ret0, _ := ret[0].(*empty.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockHealthCheckServiceMockRecorder) StartAntivirusHealthCheck(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartAntivirusHealthCheck", reflect.TypeOf((*MockHealthCheckService)(nil).StartAntivirusHealthCheck), ctx, request)
}

func TestNewHealthCheck(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockHealthCheckService(ctrl)
	healthCheck := NewHealthCheck(mockService)

	assert.NotNil(t, healthCheck)
	assert.Equal(t, mockService, healthCheck.impl)
}

func TestHealthCheck_GetAntivirusHealthCheckResult(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockHealthCheckService(ctrl)
	healthCheck := NewHealthCheck(mockService)

	ctx := context.Background()
	request := &av.GetAntiVirusHealthCheckResult_Request{}
	expectedResponse := &av.GetAntiVirusHealthCheckResult_Response{}

	mockService.EXPECT().GetAntivirusHealthCheckResult(ctx, request).Return(expectedResponse, nil)

	response, err := healthCheck.GetAntivirusHealthCheckResult(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, response)
}

func TestHealthCheck_StartAntivirusHealthCheck(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockHealthCheckService(ctrl)
	healthCheck := NewHealthCheck(mockService)

	ctx := context.Background()
	request := &av.StartAntiVirusHealthCheck_Request{}
	expectedResponse := &empty.Empty{}

	mockService.EXPECT().StartAntivirusHealthCheck(ctx, request).Return(expectedResponse, nil)

	response, err := healthCheck.StartAntivirusHealthCheck(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, response)
}
