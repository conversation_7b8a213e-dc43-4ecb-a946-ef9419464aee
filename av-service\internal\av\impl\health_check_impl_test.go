package impl

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	empty "github.com/golang/protobuf/ptypes/empty"
	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
)

func TestNewHealthCheckImpl_NilServiceProvider(t *testing.T) {
	// Test with nil service provider - should not panic
	assert.NotPanics(t, func() {
		impl := NewHealthCheckImpl(nil)
		// The function may return nil due to failed dependencies
		// We just verify it doesn't panic
		_ = impl
	})
}

func TestHealthCheckImpl_GetAntivirusHealthCheckResult_NilJobManager(t *testing.T) {
	// Test with nil job manager
	impl := &HealthCheckImpl{
		jm: nil,
	}

	ctx := context.Background()
	request := &av.GetAntiVirusHealthCheckResult_Request{
		Keys: []*av.GetAntiVirusHealthCheckResult_Request_Key{
			{
				CheckId: av.HealthCheckItem_SEC_HIDDEN_DANGER,
				Limit:   10,
			},
		},
	}

	response, err := impl.GetAntivirusHealthCheckResult(ctx, request)

	// Should return an error due to nil job manager
	assert.Nil(t, response)
	assert.Error(t, err)
	
	st, ok := status.FromError(err)
	assert.True(t, ok)
	assert.Equal(t, codes.Internal, st.Code())
}

func TestHealthCheckImpl_StartAntivirusHealthCheck_NilJobManager(t *testing.T) {
	// Test with nil job manager
	impl := &HealthCheckImpl{
		jm: nil,
	}

	ctx := context.Background()
	request := &av.StartAntiVirusHealthCheck_Request{
		CheckIds: []av.HealthCheckItem{
			av.HealthCheckItem_SEC_HIDDEN_DANGER,
			av.HealthCheckItem_ACCESS_ClOUD,
		},
	}

	response, err := impl.StartAntivirusHealthCheck(ctx, request)

	// Should return an error due to nil job manager
	assert.Nil(t, response)
	assert.Error(t, err)
	
	st, ok := status.FromError(err)
	assert.True(t, ok)
	assert.Equal(t, codes.Internal, st.Code())
}

func TestHealthCheckImpl_GetAntivirusHealthCheckResult_EmptyRequest(t *testing.T) {
	// Test with empty request
	impl := &HealthCheckImpl{
		jm: &MockJobManager{},
	}

	ctx := context.Background()
	request := &av.GetAntiVirusHealthCheckResult_Request{
		Keys: []*av.GetAntiVirusHealthCheckResult_Request_Key{},
	}

	response, err := impl.GetAntivirusHealthCheckResult(ctx, request)

	// Should handle empty request gracefully
	assert.NotNil(t, response)
	assert.NoError(t, err)
	assert.Empty(t, response.Result)
	assert.Empty(t, response.Status)
}

func TestHealthCheckImpl_GetAntivirusHealthCheckResult_NilRequest(t *testing.T) {
	// Test with nil request
	impl := &HealthCheckImpl{
		jm: &MockJobManager{},
	}

	ctx := context.Background()

	response, err := impl.GetAntivirusHealthCheckResult(ctx, nil)

	// Should handle nil request gracefully
	assert.NotNil(t, response)
	assert.NoError(t, err)
	assert.Empty(t, response.Result)
	assert.Empty(t, response.Status)
}

func TestHealthCheckImpl_StartAntivirusHealthCheck_EmptyRequest(t *testing.T) {
	// Test with empty request
	impl := &HealthCheckImpl{
		jm: &MockJobManager{},
	}

	ctx := context.Background()
	request := &av.StartAntiVirusHealthCheck_Request{
		CheckIds: []av.HealthCheckItem{},
	}

	response, err := impl.StartAntivirusHealthCheck(ctx, request)

	// Should handle empty request gracefully
	assert.NotNil(t, response)
	assert.NoError(t, err)
}

func TestHealthCheckImpl_StartAntivirusHealthCheck_NilRequest(t *testing.T) {
	// Test with nil request
	impl := &HealthCheckImpl{
		jm: &MockJobManager{},
	}

	ctx := context.Background()

	response, err := impl.StartAntivirusHealthCheck(ctx, nil)

	// Should handle nil request gracefully
	assert.NotNil(t, response)
	assert.NoError(t, err)
}

// MockJobManager implements the JobManager interface for testing
type MockJobManager struct{}

func (m *MockJobManager) GetJob(checkId av.HealthCheckItem) (JobChecker, bool) {
	return &MockJobChecker{}, true
}

func (m *MockJobManager) StopJob(checkId av.HealthCheckItem) {}

func (m *MockJobManager) GetResult(checkId av.HealthCheckItem) (*av.HealthCheckResult, *status.Status) {
	return &av.HealthCheckResult{
		CheckId: checkId,
	}, status.New(codes.OK, "").Proto()
}

func (m *MockJobManager) GetAllResults() ([]*av.HealthCheckResult, []*status.Status) {
	results := []*av.HealthCheckResult{
		{CheckId: av.HealthCheckItem_SEC_HIDDEN_DANGER},
		{CheckId: av.HealthCheckItem_ACCESS_ClOUD},
		{CheckId: av.HealthCheckItem_VIRUSLIB_VERSION},
		{CheckId: av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER},
	}
	statuses := []*status.Status{
		status.New(codes.OK, "").Proto(),
		status.New(codes.OK, "").Proto(),
		status.New(codes.OK, "").Proto(),
		status.New(codes.OK, "").Proto(),
	}
	return results, statuses
}

func (m *MockJobManager) QuickCheck(checkId av.HealthCheckItem) {}

func TestHealthCheckImpl_GetAntivirusHealthCheckResult_WithMockJobManager(t *testing.T) {
	// Test with mock job manager
	impl := &HealthCheckImpl{
		jm: &MockJobManager{},
	}

	ctx := context.Background()
	request := &av.GetAntiVirusHealthCheckResult_Request{
		Keys: []*av.GetAntiVirusHealthCheckResult_Request_Key{
			{
				CheckId: av.HealthCheckItem_SEC_HIDDEN_DANGER,
				Limit:   10,
			},
		},
	}

	response, err := impl.GetAntivirusHealthCheckResult(ctx, request)

	// Should return successful response
	assert.NotNil(t, response)
	assert.NoError(t, err)
	assert.Len(t, response.Result, 1)
	assert.Len(t, response.Status, 1)
	assert.Equal(t, av.HealthCheckItem_SEC_HIDDEN_DANGER, response.Result[0].CheckId)
}

func TestHealthCheckImpl_StartAntivirusHealthCheck_WithMockJobManager(t *testing.T) {
	// Test with mock job manager
	impl := &HealthCheckImpl{
		jm: &MockJobManager{},
	}

	ctx := context.Background()
	request := &av.StartAntiVirusHealthCheck_Request{
		CheckIds: []av.HealthCheckItem{
			av.HealthCheckItem_SEC_HIDDEN_DANGER,
			av.HealthCheckItem_ACCESS_ClOUD,
		},
	}

	response, err := impl.StartAntivirusHealthCheck(ctx, request)

	// Should return successful response
	assert.NotNil(t, response)
	assert.NoError(t, err)
	assert.IsType(t, &empty.Empty{}, response)
}

func TestHealthCheckImpl_GetAntivirusHealthCheckResult_MultipleKeys(t *testing.T) {
	// Test with multiple check IDs
	impl := &HealthCheckImpl{
		jm: &MockJobManager{},
	}

	ctx := context.Background()
	request := &av.GetAntiVirusHealthCheckResult_Request{
		Keys: []*av.GetAntiVirusHealthCheckResult_Request_Key{
			{
				CheckId: av.HealthCheckItem_SEC_HIDDEN_DANGER,
				Limit:   10,
			},
			{
				CheckId: av.HealthCheckItem_ACCESS_ClOUD,
				Limit:   20,
			},
		},
	}

	response, err := impl.GetAntivirusHealthCheckResult(ctx, request)

	// Should return successful response with multiple results
	assert.NotNil(t, response)
	assert.NoError(t, err)
	assert.Len(t, response.Result, 2)
	assert.Len(t, response.Status, 2)
}
