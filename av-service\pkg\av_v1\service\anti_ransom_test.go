package service

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/client"
	api "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/av_v1"
	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	"git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/zeus"
	empty "github.com/golang/protobuf/ptypes/empty"
)

// MockAntiRansomService is a mock implementation of AntiRansomService
type MockAntiRansomService struct {
	ctrl     *gomock.Controller
	recorder *MockAntiRansomServiceMockRecorder
}

type MockAntiRansomServiceMockRecorder struct {
	mock *MockAntiRansomService
}

func NewMockAntiRansomService(ctrl *gomock.Controller) *MockAntiRansomService {
	mock := &MockAntiRansomService{ctrl: ctrl}
	mock.recorder = &MockAntiRansomServiceMockRecorder{mock}
	return mock
}

func (m *MockAntiRansomService) EXPECT() *MockAntiRansomServiceMockRecorder {
	return m.recorder
}

func (m *MockAntiRansomService) BatchGetClientAntiransom(ctx context.Context, request *api.BatchGetClientAntiransom_Request) (*api.BatchGetClientAntiransom_Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetClientAntiransom", ctx, request)
	ret0, _ := ret[0].(*api.BatchGetClientAntiransom_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockAntiRansomServiceMockRecorder) BatchGetClientAntiransom(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetClientAntiransom", reflect.TypeOf((*MockAntiRansomService)(nil).BatchGetClientAntiransom), ctx, request)
}

func (m *MockAntiRansomService) GetClientAntiransom(ctx context.Context, request *api.GetClientAntiransom_Request) (*api.GetClientAntiransom_Response, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClientAntiransom", ctx, request)
	ret0, _ := ret[0].(*api.GetClientAntiransom_Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockAntiRansomServiceMockRecorder) GetClientAntiransom(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClientAntiransom", reflect.TypeOf((*MockAntiRansomService)(nil).GetClientAntiransom), ctx, request)
}

func (m *MockAntiRansomService) PatchAntiransomStatistics(ctx context.Context, request *av.PatchAntiRansomStatistics_Request) (*empty.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PatchAntiransomStatistics", ctx, request)
	ret0, _ := ret[0].(*empty.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

func (mr *MockAntiRansomServiceMockRecorder) PatchAntiransomStatistics(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PatchAntiransomStatistics", reflect.TypeOf((*MockAntiRansomService)(nil).PatchAntiransomStatistics), ctx, request)
}

func TestNewAntiRansom(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockAntiRansomService(ctrl)
	antiRansom := NewAntiRansom(mockService)

	assert.NotNil(t, antiRansom)
	assert.Equal(t, mockService, antiRansom.impl)
}

func TestAntiRansom_GetClientAntiransom(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockAntiRansomService(ctrl)
	antiRansom := NewAntiRansom(mockService)

	ctx := context.Background()
	request := &api.GetClientAntiransom_Request{}
	expectedResponse := &api.GetClientAntiransom_Response{}

	mockService.EXPECT().GetClientAntiransom(ctx, request).Return(expectedResponse, nil)

	response, err := antiRansom.GetClientAntiransom(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, response)
}

func TestAntiRansom_BatchGetClientAntiransom(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockAntiRansomService(ctrl)
	antiRansom := NewAntiRansom(mockService)

	ctx := context.Background()
	request := &api.BatchGetClientAntiransom_Request{}
	expectedResponse := &api.BatchGetClientAntiransom_Response{}

	mockService.EXPECT().BatchGetClientAntiransom(ctx, request).Return(expectedResponse, nil)

	response, err := antiRansom.BatchGetClientAntiransom(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, response)
}

func TestAntiRansom_PatchAntiransomStatistics_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockAntiRansomService(ctrl)
	antiRansom := NewAntiRansom(mockService)

	ctx := context.Background()
	request := &av.PatchAntiRansomStatistics_Request{
		Key: &client.ClientId{
			Id: "test-client-id",
			AssetId: &zeus.AssetId{
				Id:  1,
				Oid: 1,
			},
		},
		UpdateTime:       1234567890,
		ProtectionStatus: 1,
		DetectionTimes:   10,
		ThreatEvents:     5,
		ProtectedTimes:   8,
		RecoveredFiles:   3,
	}
	expectedResponse := &empty.Empty{}

	mockService.EXPECT().PatchAntiransomStatistics(ctx, request).Return(expectedResponse, nil)

	response, err := antiRansom.PatchAntiransomStatistics(ctx, request)

	assert.NoError(t, err)
	assert.Equal(t, expectedResponse, response)
}

func TestAntiRansom_PatchAntiransomStatistics_NilKey(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockAntiRansomService(ctrl)
	antiRansom := NewAntiRansom(mockService)

	ctx := context.Background()
	request := &av.PatchAntiRansomStatistics_Request{
		Key: nil,
	}

	response, err := antiRansom.PatchAntiransomStatistics(ctx, request)

	assert.Nil(t, response)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "ClientId is nil")
}

func TestAntiRansom_PatchAntiransomStatistics_EmptyClientId(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockAntiRansomService(ctrl)
	antiRansom := NewAntiRansom(mockService)

	ctx := context.Background()
	request := &av.PatchAntiRansomStatistics_Request{
		Key: &client.ClientId{
			Id: "",
		},
	}

	response, err := antiRansom.PatchAntiransomStatistics(ctx, request)

	assert.Nil(t, response)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "ClientId.Id is nil")
}

func TestAntiRansom_PatchAntiransomStatistics_NilAssetId(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockAntiRansomService(ctrl)
	antiRansom := NewAntiRansom(mockService)

	ctx := context.Background()
	request := &av.PatchAntiRansomStatistics_Request{
		Key: &client.ClientId{
			Id:      "test-client-id",
			AssetId: nil,
		},
	}

	response, err := antiRansom.PatchAntiransomStatistics(ctx, request)

	assert.Nil(t, response)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "ClientId.AssetId is nil")
}

func TestAntiRansom_PatchAntiransomStatistics_InvalidAssetOid(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockAntiRansomService(ctrl)
	antiRansom := NewAntiRansom(mockService)

	ctx := context.Background()
	request := &av.PatchAntiRansomStatistics_Request{
		Key: &client.ClientId{
			Id: "test-client-id",
			AssetId: &zeus.AssetId{
				Id:  1,
				Oid: 0,
			},
		},
	}

	response, err := antiRansom.PatchAntiransomStatistics(ctx, request)

	assert.Nil(t, response)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "ClientId.AssetId.Oid is nil")
}

func TestAntiRansom_PatchAntiransomStatistics_InvalidAssetId(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockAntiRansomService(ctrl)
	antiRansom := NewAntiRansom(mockService)

	ctx := context.Background()
	request := &av.PatchAntiRansomStatistics_Request{
		Key: &client.ClientId{
			Id: "test-client-id",
			AssetId: &zeus.AssetId{
				Id:  0,
				Oid: 1,
			},
		},
	}

	response, err := antiRansom.PatchAntiransomStatistics(ctx, request)

	assert.Nil(t, response)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "ClientId.AssetId.Id is nil")
}

func TestAntiRansom_PatchAntiransomStatistics_InvalidStatistics(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockService := NewMockAntiRansomService(ctrl)
	antiRansom := NewAntiRansom(mockService)

	ctx := context.Background()
	request := &av.PatchAntiRansomStatistics_Request{
		Key: &client.ClientId{
			Id: "test-client-id",
			AssetId: &zeus.AssetId{
				Id:  1,
				Oid: 1,
			},
		},
		UpdateTime:       0, // Invalid: should be > 0
		ProtectionStatus: 1,
		DetectionTimes:   10,
		ThreatEvents:     5,
		ProtectedTimes:   8,
		RecoveredFiles:   3,
	}

	response, err := antiRansom.PatchAntiransomStatistics(ctx, request)

	assert.Nil(t, response)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "AntiRansom Statistics is invalid, less than zero")
}
