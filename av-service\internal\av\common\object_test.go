package common

import (
	"testing"

	"github.com/stretchr/testify/assert"

	jarvis "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/jarvis"
)

func TestObjectEntry_BoolValue(t *testing.T) {
	tests := []struct {
		name          string
		value         interface{}
		expectedValue bool
		expectedOk    bool
	}{
		{"Valid bool true", true, true, true},
		{"Valid bool false", false, false, true},
		{"Invalid string", "test", false, false},
		{"Invalid int", 123, false, false},
		{"Invalid nil", nil, false, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entry := &ObjectEntry{value: tt.value}
			value, ok := entry.BoolValue()
			assert.Equal(t, tt.expectedValue, value)
			assert.Equal(t, tt.expectedOk, ok)
		})
	}
}

func TestObjectEntry_StringValue(t *testing.T) {
	tests := []struct {
		name          string
		value         interface{}
		expectedValue string
		expectedOk    bool
	}{
		{"Valid string", "test", "test", true},
		{"Empty string", "", "", true},
		{"Invalid bool", true, "", false},
		{"Invalid int", 123, "", false},
		{"Invalid nil", nil, "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entry := &ObjectEntry{value: tt.value}
			value, ok := entry.StringValue()
			assert.Equal(t, tt.expectedValue, value)
			assert.Equal(t, tt.expectedOk, ok)
		})
	}
}

func TestObjectEntry_IntValue(t *testing.T) {
	tests := []struct {
		name          string
		value         interface{}
		expectedValue int64
		expectedOk    bool
	}{
		{"Valid int64", int64(123), 123, true},
		{"Zero int64", int64(0), 0, true},
		{"Negative int64", int64(-123), -123, true},
		{"Invalid string", "test", 0, false},
		{"Invalid bool", true, 0, false},
		{"Invalid nil", nil, 0, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			entry := &ObjectEntry{value: tt.value}
			value, ok := entry.IntValue()
			assert.Equal(t, tt.expectedValue, value)
			assert.Equal(t, tt.expectedOk, ok)
		})
	}
}

func TestObjectEntry_Origin(t *testing.T) {
	originalEntry := &jarvis.MapObjectKeyObjectValueEntry{}
	entry := &ObjectEntry{origin: originalEntry}

	assert.Equal(t, originalEntry, entry.Origin())
}

func TestParseSecObjects_EmptyInput(t *testing.T) {
	result := ParseSecObjects([]*jarvis.MapObjectKeyObjectValueEntry{})
	assert.Len(t, result, 0)
}

func TestParseSecObjects_NilInput(t *testing.T) {
	result := ParseSecObjects(nil)
	assert.Len(t, result, 0)
}
