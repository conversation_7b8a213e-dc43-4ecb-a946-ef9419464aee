// Code generated by MockGen. DO NOT EDIT.
// Source: ./internal/av/model/health_check.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	gomock "github.com/golang/mock/gomock"
)

// MockHealthCheckModel is a mock of HealthCheckModel interface.
type MockHealthCheckModel struct {
	ctrl     *gomock.Controller
	recorder *MockHealthCheckModelMockRecorder
}

// MockHealthCheckModelMockRecorder is the mock recorder for MockHealthCheckModel.
type MockHealthCheckModelMockRecorder struct {
	mock *MockHealthCheckModel
}

// NewMockHealthCheckModel creates a new mock instance.
func NewMockHealthCheckModel(ctrl *gomock.Controller) *MockHealthCheckModel {
	mock := &MockHealthCheckModel{ctrl: ctrl}
	mock.recorder = &MockHealthCheckModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHealthCheckModel) EXPECT() *MockHealthCheckModelMockRecorder {
	return m.recorder
}

// GetAllResult mocks base method.
func (m *MockHealthCheckModel) GetAllResult(checkId int32) (*av.HealthCheckResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllResult", checkId)
	ret0, _ := ret[0].(*av.HealthCheckResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllResult indicates an expected call of GetAllResult.
func (mr *MockHealthCheckModelMockRecorder) GetAllResult(checkId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllResult", reflect.TypeOf((*MockHealthCheckModel)(nil).GetAllResult), checkId)
}

// GetResult mocks base method.
func (m *MockHealthCheckModel) GetResult(checkId int32, offset, limit int64) (*av.HealthCheckResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetResult", checkId, offset, limit)
	ret0, _ := ret[0].(*av.HealthCheckResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResult indicates an expected call of GetResult.
func (mr *MockHealthCheckModelMockRecorder) GetResult(checkId, offset, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResult", reflect.TypeOf((*MockHealthCheckModel)(nil).GetResult), checkId, offset, limit)
}

// UpdateResult mocks base method.
func (m *MockHealthCheckModel) UpdateResult(checkId int32, result *av.HealthCheckResult) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateResult", checkId, result)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateResult indicates an expected call of UpdateResult.
func (mr *MockHealthCheckModelMockRecorder) UpdateResult(checkId, result interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResult", reflect.TypeOf((*MockHealthCheckModel)(nil).UpdateResult), checkId, result)
}
