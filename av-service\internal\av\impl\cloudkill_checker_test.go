package impl

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
)

func TestCloudKillAccessableJobChecker_Constants(t *testing.T) {
	// Test that the checkMd5 constant is defined
	assert.Equal(t, "fcc6793bb86cf69d29a8d95be0e8af9f", checkMd5)
}

func TestCloudKillAccessableJobChecker_Struct(t *testing.T) {
	// Test the struct fields exist and can be initialized
	checker := &CloudKillAccessableJobChecker{
		CheckName:   "管理中心云查连通性检查",
		CheckId:     av.HealthCheckItem_ACCESS_ClOUD,
		processFlag: IDLE_JOB,
	}

	assert.Equal(t, "管理中心云查连通性检查", checker.CheckName)
	assert.Equal(t, av.HealthCheckItem_ACCESS_ClOUD, checker.CheckId)
	assert.Equal(t, int32(IDLE_JOB), checker.processFlag)
}

func TestCloudKillAccessableJobChecker_GetJobStatus(t *testing.T) {
	checker := &CloudKillAccessableJobChecker{
		processFlag: IDLE_JOB,
	}

	status := checker.GetJobStatus()
	assert.Equal(t, int32(IDLE_JOB), status)

	// Test different status values
	checker.processFlag = PORCESSING_JOB
	status = checker.GetJobStatus()
	assert.Equal(t, int32(PORCESSING_JOB), status)

	checker.processFlag = DEAD_JOB
	status = checker.GetJobStatus()
	assert.Equal(t, int32(DEAD_JOB), status)
}

func TestCloudKillAccessableJobChecker_StopJob(t *testing.T) {
	checker := &CloudKillAccessableJobChecker{
		stopCh: make(chan int, 1), // Buffered channel to avoid blocking
	}

	// This should not block or panic
	assert.NotPanics(t, func() {
		checker.StopJob()
	})

	// Verify that a signal was sent to the stop channel
	select {
	case signal := <-checker.stopCh:
		assert.Equal(t, 1, signal)
	case <-time.After(100 * time.Millisecond):
		t.Error("Expected signal on stop channel")
	}
}

func TestCloudKillAccessableJobChecker_TriggerACheck(t *testing.T) {
	checker := &CloudKillAccessableJobChecker{
		quickCh: make(chan int, 1), // Buffered channel to avoid blocking
	}

	// This should not block or panic
	assert.NotPanics(t, func() {
		checker.TriggerACheck()
	})

	// Verify that a signal was sent to the quick check channel
	select {
	case signal := <-checker.quickCh:
		assert.Equal(t, 1, signal)
	case <-time.After(100 * time.Millisecond):
		t.Error("Expected signal on quick check channel")
	}
}

func TestCloudKillAccessableJobChecker_GetCheckResult_WithMockHealthCheck(t *testing.T) {
	// Create a mock HealthCheckModel
	mockHC := &MockHealthCheckModel{
		result: &av.HealthCheckResult{
			CheckId: av.HealthCheckItem_ACCESS_ClOUD,
		},
		err: nil,
	}

	checker := &CloudKillAccessableJobChecker{
		hc: mockHC,
	}

	// This should not panic
	result, status := checker.GetCheckResult()

	// We expect some kind of result
	assert.NotNil(t, result)
	assert.NotNil(t, status)
	assert.Equal(t, av.HealthCheckItem_ACCESS_ClOUD, result.CheckId)
}

// MockHealthCheckModel implements the model.HealthCheckModel interface for testing
type MockHealthCheckModel struct {
	result *av.HealthCheckResult
	err    error
}

func (m *MockHealthCheckModel) UpdateResult(checkId int32, result *av.HealthCheckResult) error {
	return nil
}

func (m *MockHealthCheckModel) GetResult(checkId int32, offset, limit int64) (*av.HealthCheckResult, error) {
	return m.result, m.err
}

func (m *MockHealthCheckModel) GetAllResult(checkId int32) (*av.HealthCheckResult, error) {
	return m.result, m.err
}

func TestCloudKillAccessableJobChecker_StructValidation(t *testing.T) {
	// Test that the struct can be properly initialized
	checker := &CloudKillAccessableJobChecker{
		CheckName:   "管理中心云查连通性检查",
		CheckId:     av.HealthCheckItem_ACCESS_ClOUD,
		processFlag: IDLE_JOB,
	}

	assert.Equal(t, "管理中心云查连通性检查", checker.CheckName)
	assert.Equal(t, av.HealthCheckItem_ACCESS_ClOUD, checker.CheckId)
	assert.Equal(t, int32(IDLE_JOB), checker.processFlag)
}

func TestCloudKillAccessableJobChecker_ChannelInitialization(t *testing.T) {
	// Test that channels can be properly initialized
	checker := &CloudKillAccessableJobChecker{
		stopCh:  make(chan int),
		quickCh: make(chan int),
	}

	assert.NotNil(t, checker.stopCh)
	assert.NotNil(t, checker.quickCh)

	// Test that channels are not closed initially
	select {
	case <-checker.stopCh:
		t.Error("Stop channel should not have data initially")
	case <-checker.quickCh:
		t.Error("Quick channel should not have data initially")
	default:
		// This is expected - channels should be empty
	}
}

func TestCloudKillAccessableJobChecker_CheckIdValidation(t *testing.T) {
	checker := &CloudKillAccessableJobChecker{
		CheckId: av.HealthCheckItem_ACCESS_ClOUD,
	}

	// Verify the CheckId is set correctly
	assert.Equal(t, av.HealthCheckItem_ACCESS_ClOUD, checker.CheckId)

	// Test that it's the expected enum value
	assert.Equal(t, av.HealthCheckItem(1), checker.CheckId) // ACCESS_ClOUD = 1
}

func TestCloudKillAccessableJobChecker_ProcessFlagStates(t *testing.T) {
	checker := &CloudKillAccessableJobChecker{}

	// Test all possible process flag states
	states := []int32{IDLE_JOB, PORCESSING_JOB, DEAD_JOB}

	for _, state := range states {
		checker.processFlag = state
		assert.Equal(t, state, checker.GetJobStatus())
	}
}
