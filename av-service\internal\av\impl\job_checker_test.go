package impl

import (
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	"git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/utils"
)

// MockJobChecker implements the JobChecker interface for testing
type MockJobChecker struct {
	jobStatus int32
	result    *av.HealthCheckResult
	status    *status.Status
}

func (m *MockJobChecker) checkLoop() {}
func (m *Mock<PERSON>obChecker) StopJob()   {}
func (m *MockJobChecker) TriggerACheck() {}
func (m *MockJobChecker) GetCheckResult() (*av.HealthCheckResult, *status.Status) {
	return m.result, m.status
}
func (m *<PERSON><PERSON><PERSON><PERSON><PERSON>he<PERSON>) GetJobStatus() int32 {
	return m.jobStatus
}

// MockSettingsUtils implements the SettingsUtils interface for testing
type MockSettingsUtils struct {
	settings *utils.HealthCheckSettings
	err      error
}

func (m *MockSettingsUtils) GetSettings() (*utils.HealthCheckSettings, error) {
	return m.settings, m.err
}

func TestJobManager_Constants(t *testing.T) {
	// Test job status constants
	assert.Equal(t, int32(0), IDLE_JOB)
	assert.Equal(t, int32(1), PORCESSING_JOB)
	assert.Equal(t, int32(2), DEAD_JOB)
}

func TestJobManager_Initialization(t *testing.T) {
	// Test that a JobManager can be initialized
	manager := &JobManager{
		jobs:   sync.Map{},
		once:   sync.Once{},
		sutils: &MockSettingsUtils{},
	}

	assert.NotNil(t, manager)
}

func TestJobManager_GetJob(t *testing.T) {
	manager := &JobManager{
		jobs: sync.Map{},
	}

	// Test getting a non-existent job
	job, ok := manager.GetJob(av.HealthCheckItem_SEC_HIDDEN_DANGER)
	assert.False(t, ok)
	assert.Nil(t, job)

	// Test storing and getting a job
	mockJob := &MockJobChecker{jobStatus: IDLE_JOB}
	manager.jobs.Store(av.HealthCheckItem_SEC_HIDDEN_DANGER, mockJob)

	job, ok = manager.GetJob(av.HealthCheckItem_SEC_HIDDEN_DANGER)
	assert.True(t, ok)
	assert.Equal(t, mockJob, job)
}

func TestCheckCheckerEnable(t *testing.T) {
	// Test with nil settings
	assert.False(t, CheckCheckerEnable(av.HealthCheckItem_SEC_HIDDEN_DANGER, nil))

	// Test with settings where all features are disabled
	settings := &utils.HealthCheckSettings{
		HiddenSecConfigCheckEnable: false,
		CloudKillCheckEnable:       false,
		VbVersionCheckEnable:       false,
		PerformanceRiskCheckEnable: false,
	}

	assert.False(t, CheckCheckerEnable(av.HealthCheckItem_SEC_HIDDEN_DANGER, settings))
	assert.False(t, CheckCheckerEnable(av.HealthCheckItem_ACCESS_ClOUD, settings))
	assert.False(t, CheckCheckerEnable(av.HealthCheckItem_VIRUSLIB_VERSION, settings))
	assert.False(t, CheckCheckerEnable(av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER, settings))

	// Test with settings where all features are enabled
	settings = &utils.HealthCheckSettings{
		HiddenSecConfigCheckEnable: true,
		CloudKillCheckEnable:       true,
		VbVersionCheckEnable:       true,
		PerformanceRiskCheckEnable: true,
	}

	assert.True(t, CheckCheckerEnable(av.HealthCheckItem_SEC_HIDDEN_DANGER, settings))
	assert.True(t, CheckCheckerEnable(av.HealthCheckItem_ACCESS_ClOUD, settings))
	assert.True(t, CheckCheckerEnable(av.HealthCheckItem_VIRUSLIB_VERSION, settings))
	assert.True(t, CheckCheckerEnable(av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER, settings))

	// Test with settings where only some features are enabled
	settings = &utils.HealthCheckSettings{
		HiddenSecConfigCheckEnable: true,
		CloudKillCheckEnable:       false,
		VbVersionCheckEnable:       true,
		PerformanceRiskCheckEnable: false,
	}

	assert.True(t, CheckCheckerEnable(av.HealthCheckItem_SEC_HIDDEN_DANGER, settings))
	assert.False(t, CheckCheckerEnable(av.HealthCheckItem_ACCESS_ClOUD, settings))
	assert.True(t, CheckCheckerEnable(av.HealthCheckItem_VIRUSLIB_VERSION, settings))
	assert.False(t, CheckCheckerEnable(av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER, settings))

	// Test with invalid check ID
	assert.False(t, CheckCheckerEnable(av.HealthCheckItem(999), settings))
}

func TestJobManager_StopJob(t *testing.T) {
	manager := &JobManager{
		jobs: sync.Map{},
	}

	// Test stopping a non-existent job (should not panic)
	assert.NotPanics(t, func() {
		manager.StopJob(av.HealthCheckItem_SEC_HIDDEN_DANGER)
	})

	// Test stopping an existing job
	mockJob := &MockJobChecker{jobStatus: IDLE_JOB}
	manager.jobs.Store(av.HealthCheckItem_SEC_HIDDEN_DANGER, mockJob)

	assert.NotPanics(t, func() {
		manager.StopJob(av.HealthCheckItem_SEC_HIDDEN_DANGER)
	})
}

func TestJobManager_GetResult_NoSettings(t *testing.T) {
	// Test GetResult when settings are nil
	mockSettingsUtils := &MockSettingsUtils{
		settings: nil,
		err:      assert.AnError,
	}

	manager := &JobManager{
		jobs:   sync.Map{},
		sutils: mockSettingsUtils,
	}

	result, status := manager.GetResult(av.HealthCheckItem_SEC_HIDDEN_DANGER)
	
	assert.NotNil(t, result)
	assert.NotNil(t, status)
	assert.Equal(t, int32(codes.FailedPrecondition), status.Code)
}

func TestJobManager_GetAllResults_NoSettings(t *testing.T) {
	// Test GetAllResults when settings are nil
	mockSettingsUtils := &MockSettingsUtils{
		settings: nil,
		err:      assert.AnError,
	}

	manager := &JobManager{
		jobs:   sync.Map{},
		sutils: mockSettingsUtils,
	}

	results, statuses := manager.GetAllResults()
	
	assert.NotNil(t, results)
	assert.NotNil(t, statuses)
	assert.Len(t, results, 4) // Should return 4 empty results
	assert.Len(t, statuses, 4) // Should return 4 error statuses
}

func TestJobManager_QuickCheck_NoSettings(t *testing.T) {
	// Test QuickCheck when settings are nil
	mockSettingsUtils := &MockSettingsUtils{
		settings: nil,
		err:      assert.AnError,
	}

	manager := &JobManager{
		jobs:   sync.Map{},
		sutils: mockSettingsUtils,
	}

	// Should not panic
	assert.NotPanics(t, func() {
		manager.QuickCheck(av.HealthCheckItem_SEC_HIDDEN_DANGER)
	})
}
