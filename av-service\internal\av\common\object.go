package common

import (
	jarvis "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/jarvis"
)

type OEntry interface {
	BoolValue() (bool, bool) 
	StringValue() (string, bool) 
	IntValue() (int64, bool) 
	Origin() *jarvis.MapObjectKeyObjectValueEntry
}

type ObjectEntry struct {
	value interface{}
	origin *jarvis.MapObjectKeyObjectValueEntry
}

func (o *ObjectEntry) BoolValue() (bool, bool) {
	v, ok := o.value.(bool)	
	return v, ok
}

func (o *ObjectEntry) StringValue() (string, bool) {	
	v, ok := o.value.(string)	
	return v, ok
}

func (o *ObjectEntry) IntValue() (int64, bool) {
	v, ok := o.value.(int64)	
	return v, ok
}

func (o *ObjectEntry) Origin() *jarvis.MapObjectKeyObjectValueEntry {
	return o.origin
}

func ParseSecObjects(o []*jarvis.MapObjectKeyObjectValueEntry) map[string]OEntry {
	m := make(map[string]OEntry, len(o))
	
	for i := 0; i < len(o); i++ {
		object := o[i]

		key := 	object.GetKey().GetItemName()
		var value interface{}
		if object.GetValue().GetBoolScalar() != nil {
			value = object.GetValue().GetBoolScalar().GetValue()
		} else if object.GetValue().GetStringScalar() != nil {
			value = object.GetValue().GetStringScalar().GetValue()
		} else if object.GetValue().GetIntScalar() != nil {
			value = object.GetValue().GetIntScalar().GetValue()
		} else {
			continue //其他类型暂时不关注
		}

		oe := &ObjectEntry{
			value: value,
			origin: object,
		}

		
		m[key] = oe
	}
	return m 
}