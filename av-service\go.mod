module git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git

go 1.23.2

require (
	git-biz.qianxin-inc.cn/ats-rd2-infra-components/license/license-component-api.git v0.0.40
	git-biz.qianxin-inc.cn/ats-rd2-infra-components/sdk/database/sorm-driver-plugin.git v0.0.53
	git-biz.qianxin-inc.cn/ats-rd2-infra-components/sdk/database/sorm-plugin.git v0.0.19
	git-biz.qianxin-inc.cn/ats-rd2-infra-components/sdk/sorm.git v0.0.106
	git-biz.qianxin-inc.cn/infra-components/data-access-framework/golang/data-access-go.git v1.19.22
	git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git v1.0.378
	git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git v2.19.0-alpha.16+incompatible
	git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git v0.0.2-rc.1
	git-biz.qianxin-inc.cn/zeus-platform/data-access-cross-plugin.git v0.0.100
	git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git v3.10.0-rc.3+incompatible
	git-biz.qianxin-inc.cn/zion-infra/styx/styx-protocol.git v0.9.10
	github.com/golang/mock v1.3.1
	github.com/golang/protobuf v1.5.4
	github.com/pkg/errors v0.9.1
	github.com/robfig/cron/v3 v3.0.1
	github.com/stretchr/testify v1.8.4
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240814211410-ddb44dafa142
	google.golang.org/grpc v1.67.2
)

require (
	git-biz.qianxin-inc.cn/ats-rd2-infra-components/api/auth2-api.git v0.2.3 // indirect
	git-biz.qianxin-inc.cn/ats-rd2-infra-components/api/cascade2-api.git v0.0.29 // indirect
	git-biz.qianxin-inc.cn/ats-rd2-infra-components/license/license-cloud-api.git v0.2.60 // indirect
	git-biz.qianxin-inc.cn/ats-rd2-infra-components/sdk/database/database-config.git v0.0.5 // indirect
	git-biz.qianxin-inc.cn/ats-rd2-infra-components/service/edge2/edge2-config-api.git v0.0.3 // indirect
	git-biz.qianxin-inc.cn/ats-rd2-infra-components/service/edge2/edge2-protocol-connlib.git v0.0.155 // indirect
	git-biz.qianxin-inc.cn/ats-rd2-infra-components/service/edge2/edge2-protocol.git v0.0.178 // indirect
	git-biz.qianxin-inc.cn/infra-components/data-access-framework/golang/data-access-deploy-lib.git v1.0.6 // indirect
	git-biz.qianxin-inc.cn/infra-components/data-access-framework/golang/data-access-protocol-go.git v1.0.8 // indirect
	git-biz.qianxin-inc.cn/infra-components/data-access-framework/golang/graphql.git v0.8.0 // indirect
	git-biz.qianxin-inc.cn/infra-components/data-access-framework/golang/helper/data-access-go-helper.git v0.2.0-alpha.5-tmp // indirect
	git-biz.qianxin-inc.cn/infra-components/dcs2/dcs-api.git v0.0.3 // indirect
	git-biz.qianxin-inc.cn/infra-components/dcs2/dcs-client.git v0.0.7 // indirect
	git-biz.qianxin-inc.cn/infra-components/sdk/key-generator.git v1.5.4 // indirect
	git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/config-encryptor.git v0.0.4 // indirect
	git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework-plugins.git v1.0.175 // indirect
	git-biz.qianxin-inc.cn/infra-components/sdk/retry.git v1.0.4 // indirect
	git-biz.qianxin-inc.cn/skylar-ng/skylar-public-components/indigo-api.git v0.3.7 // indirect
	git-biz.qianxin-inc.cn/zeus-platform/themis-api.git v0.0.1 // indirect
	git-open.qianxin-inc.cn/free/database/go-driver/dm-driver.git v0.0.14 // indirect
	git-open.qianxin-inc.cn/free/database/go-driver/kingbase-driver.git v0.0.2 // indirect
	git-open.qianxin-inc.cn/free/memberlist v0.0.3 // indirect
	git-open.qianxin-inc.cn/free/pkix.git v0.0.3 // indirect
	github.com/ClickHouse/clickhouse-go v1.5.4 // indirect
	github.com/NYTimes/gziphandler v1.1.1 // indirect
	github.com/StackExchange/wmi v0.0.0-20190523213315-cbe66965904d // indirect
	github.com/ahmetb/go-linq/v3 v3.2.0 // indirect
	github.com/alexbrainman/odbc v0.0.0-20230814102256-1421b829acc9 // indirect
	github.com/armon/go-metrics v0.4.1 // indirect
	github.com/aws/aws-sdk-go v1.34.28 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cenkalti/backoff/v4 v4.3.0 // indirect
	github.com/cep21/circuit/v3 v3.2.2 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/cheekybits/genny v1.0.0 // indirect
	github.com/cloudflare/golz4 v0.0.0-20150217214814-ef862a3cdc58 // indirect
	github.com/coreos/go-semver v0.3.1 // indirect
	github.com/coreos/go-systemd/v22 v22.5.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/desertbit/timer v0.0.0-20180107155436-c41aec40b27f // indirect
	github.com/dgryski/go-metro v0.0.0-20211217172704-adc40b04c140 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/fatih/color v1.16.0 // indirect
	github.com/fatih/pool v3.0.0+incompatible // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/go-ini/ini v1.67.0 // indirect
	github.com/go-ole/go-ole v1.3.0 // indirect
	github.com/go-sql-driver/mysql v1.5.0 // indirect
	github.com/go-stack/stack v1.8.0 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/btree v1.1.2 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/gorilla/mux v1.8.1 // indirect
	github.com/grpc-ecosystem/go-grpc-prometheus v1.2.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway v1.16.0 // indirect
	github.com/hashicorp/consul/api v1.28.2 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-cleanhttp v0.5.2 // indirect
	github.com/hashicorp/go-hclog v1.6.3 // indirect
	github.com/hashicorp/go-immutable-radix v1.3.1 // indirect
	github.com/hashicorp/go-metrics v0.5.3 // indirect
	github.com/hashicorp/go-msgpack v0.5.5 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/go-retryablehttp v0.7.7 // indirect
	github.com/hashicorp/go-rootcerts v1.0.2 // indirect
	github.com/hashicorp/go-sockaddr v1.0.6 // indirect
	github.com/hashicorp/golang-lru v1.0.2 // indirect
	github.com/hashicorp/serf v0.10.1 // indirect
	github.com/huandu/skiplist v0.0.0-20180112095830-8e883b265e1b // indirect
	github.com/iancoleman/strcase v0.3.0 // indirect
	github.com/improbable-eng/grpc-web v0.15.0 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20221227161230-091c0ba34f0a // indirect
	github.com/jackc/pgx/v5 v5.5.4 // indirect
	github.com/jackc/puddle/v2 v2.2.1 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/klauspost/compress v1.17.8 // indirect
	github.com/lestrrat-go/strftime v1.0.6 // indirect
	github.com/lib/pq v1.10.1 // indirect
	github.com/marten-seemann/qtls v0.3.2 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-sqlite3 v2.0.3+incompatible // indirect
	github.com/miekg/dns v1.1.59 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/natefinch/npipe v0.0.0-20160621034901-c1b8fa8bdcce // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/orcaman/concurrent-map v1.0.0 // indirect
	github.com/panmari/cuckoofilter v1.0.6 // indirect
	github.com/pelletier/go-toml v1.9.5 // indirect
	github.com/pierrec/lz4 v2.6.0+incompatible // indirect
	github.com/pingcap/errors v0.11.4 // indirect
	github.com/pingcap/failpoint v0.0.0-20210316064728-7acb0f0a3dfd // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/prometheus/client_golang v1.19.1 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.53.0 // indirect
	github.com/prometheus/procfs v0.14.0 // indirect
	github.com/redis/go-redis/v9 v9.7.0 // indirect
	github.com/rs/cors v1.11.0 // indirect
	github.com/sean-/seed v0.0.0-20170313163322-e2103e2c3529 // indirect
	github.com/shirou/gopsutil/v3 v3.20.11 // indirect
	github.com/soheilhy/cmux v0.1.5 // indirect
	github.com/uber/jaeger-client-go v2.30.0+incompatible // indirect
	github.com/uber/jaeger-lib v2.4.1+incompatible // indirect
	github.com/xdg-go/pbkdf2 v1.0.0 // indirect
	github.com/xdg-go/scram v1.0.2 // indirect
	github.com/xdg-go/stringprep v1.0.2 // indirect
	github.com/xhit/go-str2duration/v2 v2.1.0 // indirect
	github.com/youmark/pkcs8 v0.0.0-20181117223130-1be2e3e5546d // indirect
	go.etcd.io/etcd/api/v3 v3.5.13 // indirect
	go.etcd.io/etcd/client/pkg/v3 v3.5.13 // indirect
	go.etcd.io/etcd/client/v3 v3.5.13 // indirect
	go.mongodb.org/mongo-driver v1.5.4 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/automaxprocs v1.5.3 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.27.0 // indirect
	golang.org/x/crypto v0.35.0 // indirect
	golang.org/x/exp v0.0.0-20240506185415-9bf2ced13842 // indirect
	golang.org/x/mod v0.17.0 // indirect
	golang.org/x/net v0.33.0 // indirect
	golang.org/x/sync v0.11.0 // indirect
	golang.org/x/sys v0.30.0 // indirect
	golang.org/x/text v0.22.0 // indirect
	golang.org/x/time v0.5.0 // indirect
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	google.golang.org/genproto v0.0.0-20240509183442-62759503f434 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20240814211410-ddb44dafa142 // indirect
	google.golang.org/protobuf v1.34.2 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	nhooyr.io/websocket v1.8.11 // indirect
)
