package main 

import (
	_ "git-biz.qianxin-inc.cn/ats-rd2-infra-components/sdk/database/sorm-plugin.git"
	"git-biz.qianxin-inc.cn/infra-components/data-access-framework/golang/data-access-go.git/da"
	_ "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/pkg/av_v1"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	cross_api "git-biz.qianxin-inc.cn/zeus-platform/data-access-cross-plugin.git/cross/api"
	_ "git-biz.qianxin-inc.cn/zeus-platform/data-access-cross-plugin.git/cross/init"
	_ "git-biz.qianxin-inc.cn/ats-rd2-infra-components/sdk/database/sorm-driver-plugin.git"
)

func main() {
	defer da.Init()()

	cross_api.RegisterAllService()

	
	if err := da.RunServer(); err != nil {
		gflog.Fatalf("Run Service err=%v", err.Error())
	}
}
