package utils

import (
	"context"
	//"errors"
	"fmt"
	"git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/asset_v1"
	"git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/asset_v1/client"
	"git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/org_v1"
	org_client "git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/org_v1/client"
	"git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/zeus"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	//"time"
)

var _assetId *zeus.AssetId

func AssetId() (*zeus.AssetId, error) {
	if _assetId == nil {
		ctx, cancelFn := context.WithCancel(context.Background())
		defer cancelFn()
		assetCli, err := client.NewAssetV1ThinClient()
		if err != nil {
			return nil, err
		}

		// 得到最新的assetId，在控制台未初始化前，可能报错
		assetId, err := GetNewestAssetId(ctx, assetCli)
		if err != nil {
			gflog.Warn("GetNewestAssetId error:", err)
			return nil, err
		} else {
			setAssetId(assetId)
		}
	}

	return _assetId, nil
}

func setAssetId(id *zeus.AssetId) {
	_assetId = id
}

func GetNewestAssetId(ctx context.Context, assetCli asset_v1.AssetV1Client) (*zeus.AssetId, error) {
	orgId, err := getOrgId(ctx)
	if err != nil {
		return nil, err
	}

	assetId, err := getOrgAssets(ctx, assetCli, orgId)
	if err != nil {
		return nil, err
	}

	return assetId, nil
}

//从宙斯获取oid
func getOrgId(ctx context.Context) (int64, error) {
	orgCli, err := org_client.NewOrgV1ThinClient()
	if err != nil {
		gflog.Errorf("org_client.NewOrgV1ThinClient fail.... err=%v", err)
		return 0, err
	}

	resp, err := orgCli.GetOrgsWithFilterFilter(ctx, &org_v1.GetOrgsWithFilterFilter_Request{})
	if err != nil {
		gflog.Errorf("orgCli.GetOrgsWithFilterFilter fail.... err=%v", err)
		return 0, err
	}

	gflog.Info("subscribeAssetId GetOrgsWithFilterFilter Ok")
	if resp == nil || len(resp.GetOrgs()) < 1 {
		return 0, fmt.Errorf("asset oid not fund err")
	}

	return resp.GetOrgs()[0], nil
}

func getOrgAssets(ctx context.Context, assetCli asset_v1.AssetV1Client, oid int64) (*zeus.AssetId, error) {
	assetResp, err := assetCli.GetOrgAssets(ctx, &asset_v1.GetOrgAssets_Request{Key: oid})
	if err != nil {
		gflog.Errorf("assetCli.GetOrgAssets fail.... err=%v", err)
		return nil, err
	}

	gflog.Info("subscribeAssetId GetOrgAssets Ok")
	if assetResp == nil || len(assetResp.GetAssets()) < 1 {
		return nil, fmt.Errorf("asset not fund err")
	}

	return assetResp.GetAssets()[0], nil
}
