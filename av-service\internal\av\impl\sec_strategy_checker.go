package impl 

import (
	"time"
	"context"
	"errors"
	"sync/atomic"
	"github.com/robfig/cron/v3"
	"github.com/golang/protobuf/ptypes/timestamp"
	"google.golang.org/grpc/codes"
	status "google.golang.org/genproto/googleapis/rpc/status"
	empty "github.com/golang/protobuf/ptypes/empty"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
	//gen_type "git-biz.qianxin-inc.cn/zion-infra/styx/styx-protocol.git/generated-go/zion/styx/gen_type"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	"git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	model "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/model"
	common "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/common"
	jarvis "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/jarvis"
	"git-biz.qianxin-inc.cn/ats-rd2-infra-components/license/license-component-api.git/generated-go/license_component"
	"git-biz.qianxin-inc.cn/ats-rd2-infra-components/license/license-component-api.git/generated-go/license_component_v1"
	licenseclient "git-biz.qianxin-inc.cn/ats-rd2-infra-components/license/license-component-api.git/generated-go/license_component_v1/client/thin"
)

type SecStrategyJobChecker struct {
	stopCh 		chan int
	quickCh 	chan int 
	CheckName 	string
	CheckId 	av.HealthCheckItem 
	cJob        *cron.Cron
	client      license_component_v1.LicenseComponentV1Client
	processFlag int32 //为了避免并发的多次检查
	hc          model.HealthCheckModel
}


func NewSecStrategyJobChecker(sp server.ServiceProvider, firstCheck bool) JobChecker {

	cli, err := licenseclient.NewLicenseComponentV1ThinClient()
	if err != nil {
		gflog.Errorf("create license client failed, err: %v", err)
		return nil
	}


	hc := CreateHealthCheckModel(int32(av.HealthCheckItem_SEC_HIDDEN_DANGER), sp)
	if hc == nil {
		gflog.Errorf("CreateHealthCheckModel failed")
		return nil
	}

	checker := &SecStrategyJobChecker{
		CheckName: "病毒防护策略-部署阶段的配置项检查",
		CheckId: av.HealthCheckItem_SEC_HIDDEN_DANGER,
		quickCh: make(chan int),
		stopCh: make(chan int),
		client: cli,
		processFlag: IDLE_JOB,
		hc: hc,
	}

	c := cron.New(cron.WithSeconds())
	c.Start()
	_, err = c.AddFunc("@midnight", checker.check)
    if err != nil {
        gflog.Errorf("add check to cron job failed err=%v", err)
        return nil	
    }
	checker.cJob = c

	go checker.checkLoop()
	//半小时后检查一次
	if firstCheck {
		go func() {
			time.Sleep(1800 * time.Second)
			checker.check()
		}()
	}
	return checker
}

func (c *SecStrategyJobChecker) checkLoop() {
	gflog.Debugf("SecStrategyJobChecker checkLoop start ...")
	defer c.cJob.Stop() //退出之后停掉定时器
	defer atomic.StoreInt32(&c.processFlag, DEAD_JOB)
	//job只要初始化，就立即检查一次。
	//1. 开关重新开启 2. 服务重新启动 3. 服务第一次安装运行
	c.check()
	for {
		select {
		case <-c.stopCh:
			gflog.Infof("SecStrategyJobChecker is shut down...")
			return 
		case <-c.quickCh:
			c.check()
		}
	}
}

func (c *SecStrategyJobChecker) check() {
	if !atomic.CompareAndSwapInt32(&c.processFlag, IDLE_JOB, PORCESSING_JOB) {
		gflog.Debugf("SecStrategyJobChecker is checking ...")
		return
	}
	defer atomic.StoreInt32(&c.processFlag, IDLE_JOB)

	gflog.Debugf("SecStrategyJobChecker check ...")
	
	//1. 获取天擎守护天数
	activateTime, err := c.GetActivateTime()
	if err != nil {
		gflog.Errorf("GetActivateTime failed err=%v", err)
		return
	}
	
	sub := time.Now().Unix() - activateTime
	days := int(sub / 86400)
	
	gflog.Debugf("SecStrategyJobChecker Get ActivateTime=%v Days=%v sub=%v", activateTime, days, sub)
	
	if days < 30 {
		gflog.Infof("SecStrategyJobChecker ActivateTime=%v Days=%v less than 30", activateTime, days)
		return 
	}

	pUtils := GetPolicyUtils()	
	suspectPolicies := []*av.PolicyCheckResult{}
	
	policies, metas, err := pUtils.GetAntiVirusPoliciesPartially(context.Background(), 20)
	if err != nil {
		//遇到错误直接退出
		gflog.Errorf("GetAntiVirusPoliciesPartially failed err=%v", err)
		return 
	}

	for i := 0; i < len(policies); i++ {
		//2. 检查策略项
		policy := policies[i]
		meta := metas[i]
		p, isInvaild := checkPolicyWithClientType(policy, meta.GetProject().GetClientType())
		if isInvaild {
			suspectPolicies = append(suspectPolicies, p)
		}
	}	

	var result *av.HealthCheckResult
	//只要出现这种日志就需要提醒
	if len(suspectPolicies) > 0 {
		result = &av.HealthCheckResult{
			CheckId:  av.HealthCheckItem_SEC_HIDDEN_DANGER,
			ItemName: c.CheckName,
			Policies: suspectPolicies,
			CheckTime:  &timestamp.Timestamp{
				Seconds: time.Now().Unix(),
			},
		}	
	}

	//更新到数据库
	c.hc.UpdateResult(int32(av.HealthCheckItem_SEC_HIDDEN_DANGER), result)
}

func (c *SecStrategyJobChecker) StopJob() {
	c.stopCh <- 1
}

func (c *SecStrategyJobChecker) TriggerACheck() {
	c.quickCh <- 1
}

func (c *SecStrategyJobChecker) GetCheckResult() (*av.HealthCheckResult, *status.Status) {
	//查询数据库
	result, err := c.hc.GetAllResult(int32(av.HealthCheckItem_SEC_HIDDEN_DANGER))
	if err != nil {
		gflog.Errorf("SecStrategyJobChecker GetCheckResult failed err=%v", err)
		return &av.HealthCheckResult{}, &status.Status{Code: int32(codes.FailedPrecondition), Message: err.Error()}
	}
	gflog.Debugf("SecStrategyJobChecker GetCheckResult result=%v", result)
	return result, &status.Status{}
}

func (c *SecStrategyJobChecker) GetJobStatus() int32 {
	return atomic.LoadInt32(&c.processFlag)
}

func checkPolicyWithClientType(policy *av.PolicyCheckResult, clientType string) (*av.PolicyCheckResult, bool) {
	
	objects := common.ParseSecObjects(policy.GetObjects())
	//只记录跟敏感项项有关系的Object就可以
	policy.Objects = []*jarvis.MapObjectKeyObjectValueEntry{}

	if clientType != "xc" && clientType != "xcser" && clientType != "linuxserver" {
		//1. QCE
		if o, ok := checkScanner("scanners.qce.enabled", "scanners.qce.silent.enabled", objects); ok {
			policy.Objects = append(policy.Objects, o)
		}

		//2. QRE
		if o, ok := checkScanner("scanners.qre.enabled", "scanners.qre.silent.enabled", objects); ok {
			policy.Objects = append(policy.Objects, o)
		}

		//3. QOWL
		if o, ok := checkScanner("scanners.qowl.enabled", "scanners.qowl.silent.enabled", objects); ok {
			policy.Objects = append(policy.Objects, o)
		}

		//4. QDE
		if o, ok := checkScanner("scanners.qde.enabled", "scanners.qde.silent.enabled", objects); ok {
			policy.Objects = append(policy.Objects, o)
		}

		//5. bd
		if o, ok := checkScanner("scanners.bd.enabled", "scanners.bd.silent.enabled", objects); ok {
			policy.Objects = append(policy.Objects, o)
		}

		//6.实时防护
		if t, ok := objects["realtime_defense.virus_treatment"]; ok {
			if v, ok := t.IntValue(); ok {
				if v == 3 {
					policy.Objects = append(policy.Objects, t.Origin())
				}
			}
		} 

		//7.病毒扫描
		if t, ok := objects["scan_object.virus_treatment"]; ok {
			if v, ok := t.IntValue(); ok {
				if v == 3 {
					policy.Objects = append(policy.Objects, t.Origin())
				}
			}
		} 

		//8.主动防御
		if t, ok := objects["active_defense.virus_treatment"]; ok {
			if v, ok := t.IntValue(); ok {
				if v == 3 {
					policy.Objects = append(policy.Objects, t.Origin())
				}
			}
		} 
	} else {
		//病毒扫描 信创只需要检测病毒处理日志
		if t, ok := objects["scan_object.virus_action"]; ok {
			if v, ok := t.StringValue(); ok {
				if v == "log" {
					policy.Objects = append(policy.Objects, t.Origin())
				}
			}
		} 

		//病毒扫描 信创只需要检测病毒处理日志
		if t, ok := objects["defense_operation_type.virus_action"]; ok {
			if v, ok := t.StringValue(); ok {
				if v == "log" {
					policy.Objects = append(policy.Objects, t.Origin())
				}
			}
		} 

		//主动防御
		if t, ok := objects["active_defense.virus_treatment"]; ok {
			if v, ok := t.IntValue(); ok {
				if v == 3 {
					policy.Objects = append(policy.Objects, t.Origin())
				}
			}
		} 
	}

	return policy, len(policy.Objects) > 0
}

func checkScanner(enableKey, slientKey string, objects map[string]common.OEntry) (*jarvis.MapObjectKeyObjectValueEntry, bool) {
	enableEntry, exist1 := objects[enableKey]
	slientEntry, exist2 := objects[slientKey]
	
	if !exist1 || !exist2 { //如果没有这两项，不用检查
		return nil, false
	}

	enable, ok1 := enableEntry.BoolValue()
	slient, ok2 := slientEntry.BoolValue()

	if ok1 && ok2 && slient && enable {
		return slientEntry.Origin(), true
	}

	return nil, false
}

func (c *SecStrategyJobChecker) GetActivateTime() (int64, error) {
	
	// 获取deployid
	deployResp, err := c.client.GetDeploy(context.Background(), &empty.Empty{})
	if err != nil {
		gflog.Errorf("get deploy failed, err: %v", err)
		return 0, errors.New("get deploy failed, err: "+ err.Error())
	}

	if deployResp == nil ||  deployResp.DeployId == "" {
		gflog.Errorf("deployId is empty, can not load license info")
		return 0, errors.New("deployId is empty, can not load license info")
	}

	gflog.Infof("deployId: %v", deployResp.DeployId)

	// 获取激活信息
	resp, err := c.client.GetLicenseContent(context.Background(), &license_component.GetLicenseContentRequest{
		DeployId: deployResp.DeployId,
	})

	if err != nil {
		gflog.Errorf("get license content failed, err: %v", err)
		return 0, errors.New("get license content failed, err: "+ err.Error())
	}

	if resp.LicenseFile == nil || resp.LicenseFile.ActivateTime == nil {
		gflog.Errorf("license file is nil")
		return 0, errors.New("license file is nil")
	}

	gflog.Infof("resp.LicenseFile.ActivateTime=%v", resp.LicenseFile.ActivateTime)
	return resp.LicenseFile.ActivateTime.Seconds, nil
}

