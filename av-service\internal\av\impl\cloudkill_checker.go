package impl 

import (
	"time"
	"context"
	"sync/atomic"
	"encoding/hex"
	"github.com/robfig/cron/v3"
	"github.com/golang/protobuf/ptypes/timestamp"
	"google.golang.org/grpc/codes"
	status "google.golang.org/genproto/googleapis/rpc/status"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
	jarvis "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/jarvis"
	//gen_type "git-biz.qianxin-inc.cn/zion-infra/styx/styx-protocol.git/generated-go/zion/styx/gen_type"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	"git-biz.qianxin-inc.cn/zion-infra/styx/styx-protocol.git/generated-go/zion/styx"
	"git-biz.qianxin-inc.cn/zion-infra/styx/styx-protocol.git/generated-go/file_v1"
	fcli "git-biz.qianxin-inc.cn/zion-infra/styx/styx-protocol.git/generated-go/file_v1/client"
	"git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	model "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/model"
	zeus "git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/zeus"
)

var checkMd5 = "fcc6793bb86cf69d29a8d95be0e8af9f"

type CloudKillAccessableJobChecker struct {
	stopCh 		chan int
	quickCh 	chan int 
	CheckName 	string
	CheckId 	av.HealthCheckItem 
	cJob		*cron.Cron
	//ticker 	*time.Ticker
	fClient		file_v1.FileV1Client
	processFlag int32 //为了避免并发的多次检查
	hc          model.HealthCheckModel
}

func NewCloudKillAccessableJobChecker(sp server.ServiceProvider, firstCheck bool) JobChecker{
	
	cli, _ := fcli.NewFileV1ThinClient()
	if cli == nil {
		gflog.Errorf("CreateFileV1ThinClient failed")
		return nil
	}

	hc := CreateHealthCheckModel(int32(av.HealthCheckItem_ACCESS_ClOUD), sp)
	if hc == nil {
		gflog.Errorf("CreateHealthCheckModel failed")
		return nil
	}

	checker := &CloudKillAccessableJobChecker{
		CheckName: "管理中心云查连通性检查",
		CheckId: av.HealthCheckItem_ACCESS_ClOUD,
		//ticker: time.NewTicker(30 * time.Minute),
		fClient: cli,
		quickCh: make(chan int),
		stopCh: make(chan int),
		processFlag: IDLE_JOB,
		hc: hc,
	}

	c := cron.New(cron.WithSeconds())
	c.Start()
	_, err := c.AddFunc("@hourly", checker.check)
    if err != nil {
        gflog.Errorf("add check to cron job failed err=%v", err)
        return nil	
    }
	checker.cJob = c

	go checker.checkLoop()
	//半小时后检查一次
	if firstCheck {
		go func() {
			time.Sleep(1800 * time.Second)
			checker.check()
		}()
	}
	
	return checker
}


func (c *CloudKillAccessableJobChecker) checkLoop() {
	gflog.Debugf("CloudKillAccessableJobChecker checkLoop start ...")
	defer atomic.StoreInt32(&c.processFlag, DEAD_JOB)
	//job只要初始化，就立即检查一次。
	//1. 开关重新开启 2. 服务重新启动 3. 服务第一次安装运行
	defer c.cJob.Stop() //关闭定时任务
	c.check()
	for {
		select {
		case <-c.stopCh:
			gflog.Infof("CloudKillAccessableJobChecker is shut down...")
			return 
		case <-c.quickCh:
			c.check()
		}
	}
}

func (c *CloudKillAccessableJobChecker) check() {
	
	if !atomic.CompareAndSwapInt32(&c.processFlag, IDLE_JOB, PORCESSING_JOB) {
		gflog.Debugf("CloudKillAccessableJobChecker is checking ...")
		return
	}

	defer atomic.StoreInt32(&c.processFlag, IDLE_JOB)	
	gflog.Debugf("CloudKillAccessableJobChecker check ...")

	pUtils := GetPolicyUtils()
	//1. 检查策略项
	suspectPolicies := []*av.PolicyCheckResult{}
	var result *av.HealthCheckResult

	policies, _, err := pUtils.GetAntiVirusPoliciesPartially(context.Background(), 20)
	if err != nil {
		//遇到错误直接退出
		gflog.Errorf("GetAntiVirusPoliciesPartially failed err=%v", err)
		return 
	}

	for i := 0; i < len(policies); i++ {
		policy := policies[i]
		for j := 0; j < len(policy.GetObjects()); j++ {
			object := policy.Objects[j]
			if object.GetKey().GetItemName() == "scanners.qce.connection.mode" { 
				if object.GetValue().GetStringScalar().GetValue() == "console_2_public" {
					//只记录跟敏感项项有关系的Object就可以
					policy.Objects = []*jarvis.MapObjectKeyObjectValueEntry{object}
					suspectPolicies = append(suspectPolicies, policy)
				}
			}
		}
	}

	gflog.Debugf("CloudKillAccessableJobChecker suspectPolicies len=%v suspectPolicies=%v", len(suspectPolicies), suspectPolicies)
	
	//当出现云查模式为通过代理服务器连接云查的时候，才需要云查
	if len(suspectPolicies) > 0 {
		//2. 检查云查是否通
		keys := []*file_v1.GetFileSecurity_Request{}
		m, _ := hex.DecodeString(checkMd5)

		req := &file_v1.GetFileSecurity_Request{
			Key: &styx.FileId{
				Md5:  m,
				AssetId: &zeus.AssetId{
					Id:  suspectPolicies[0].Key.GetAssetId().GetId(),
					Oid: suspectPolicies[0].Key.GetAssetId().GetOid(),
				},
			},
		}
		keys = append(keys, req)
		
		request := &file_v1.BatchGetFileSecurity_Request{
			Keys:    keys,
		}

		isAccessable := false
		gflog.Debugf("CloudKillAccessableJobChecker check isAccessable ....")
		for i := 0; i < 5; i++ { //最多重试五次
			
			ctx_c, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			resp, err := c.fClient.BatchGetFileSecurity(ctx_c, request)
			if err != nil {
				gflog.Errorf("BatchGetFileSecurity failed: %v", err)
				time.Sleep(1 * time.Minute)
				continue
			}

			stat := resp.GetStatus()
			if len(stat) > 0 {
				if stat[0].Code != int32(codes.OK) && stat[0].Code != int32(codes.NotFound) {
					gflog.Errorf("BatchGetFileSecurity failed: %v", stat[0])
					time.Sleep(1 * time.Minute)
					continue
				}
			}
			isAccessable = true
			break
		}

		//3. 检测到云查不通，应该记录result
		if !isAccessable {
			result = &av.HealthCheckResult{
				CheckId:  av.HealthCheckItem_ACCESS_ClOUD,
				ItemName: c.CheckName,
				Policies: suspectPolicies,
				CheckTime: &timestamp.Timestamp{
					Seconds: time.Now().Unix(),
				},
			}
			gflog.Debugf("CloudKillAccessableJobChecker cloud stategy and connection has some problem result=%v", result)
		}
	}

	//更新到数据库
	c.hc.UpdateResult(int32(av.HealthCheckItem_ACCESS_ClOUD), result)
}

func (c *CloudKillAccessableJobChecker) StopJob() {
	c.stopCh <- 1
}

func (c *CloudKillAccessableJobChecker) TriggerACheck() {
	c.quickCh <- 1
}

func (c *CloudKillAccessableJobChecker) GetCheckResult() (*av.HealthCheckResult, *status.Status) {
	//查询数据库
	result, err := c.hc.GetAllResult(int32(av.HealthCheckItem_ACCESS_ClOUD))
	if err != nil {
		gflog.Errorf("CloudKillAccessableJobChecker GetCheckResult failed err=%v", err)
		return &av.HealthCheckResult{}, &status.Status{Code: int32(codes.FailedPrecondition), Message: err.Error()}
	}
	gflog.Debugf("CloudKillAccessableJobChecker GetCheckResult result=%v", result)
	return result, &status.Status{}
}

func (c *CloudKillAccessableJobChecker) GetJobStatus() int32 {
	return atomic.LoadInt32(&c.processFlag)
}
