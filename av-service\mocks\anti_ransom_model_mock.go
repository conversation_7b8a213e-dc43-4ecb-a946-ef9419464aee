// Code generated by MockGen. DO NOT EDIT.
// Source: ./internal/av/model/antiransom.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	model "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/model"
	client "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/client"
	gomock "github.com/golang/mock/gomock"
)

// MockAntiRansomModel is a mock of AntiRansomModel interface.
type MockAntiRansomModel struct {
	ctrl     *gomock.Controller
	recorder *MockAntiRansomModelMockRecorder
}

// MockAntiRansomModelMockRecorder is the mock recorder for MockAntiRansomModel.
type MockAntiRansomModelMockRecorder struct {
	mock *MockAntiRansomModel
}

// NewMockAntiRansomModel creates a new mock instance.
func NewMockAntiRansomModel(ctrl *gomock.Controller) *MockAntiRansomModel {
	mock := &MockAntiRansomModel{ctrl: ctrl}
	mock.recorder = &MockAntiRansomModelMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAntiRansomModel) EXPECT() *MockAntiRansomModelMockRecorder {
	return m.recorder
}

// FindOne mocks base method.
func (m *MockAntiRansomModel) FindOne(clientId *client.ClientId) (*model.AntiRansomDTO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOne", clientId)
	ret0, _ := ret[0].(*model.AntiRansomDTO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOne indicates an expected call of FindOne.
func (mr *MockAntiRansomModelMockRecorder) FindOne(clientId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOne", reflect.TypeOf((*MockAntiRansomModel)(nil).FindOne), clientId)
}

// Insert mocks base method.
func (m *MockAntiRansomModel) Insert(data *model.AntiRansomDTO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", data)
	ret0, _ := ret[0].(error)
	return ret0
}

// Insert indicates an expected call of Insert.
func (mr *MockAntiRansomModelMockRecorder) Insert(data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockAntiRansomModel)(nil).Insert), data)
}

// Update mocks base method.
func (m *MockAntiRansomModel) Update(data *model.AntiRansomDTO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", data)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockAntiRansomModelMockRecorder) Update(data interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockAntiRansomModel)(nil).Update), data)
}
