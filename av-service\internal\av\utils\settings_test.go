package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHealthCheckSettings_Struct(t *testing.T) {
	settings := &HealthCheckSettings{
		HiddenSecConfigCheckEnable: true,
		CloudKillCheckEnable:       false,
		VbVersionCheckEnable:       true,
		PerformanceRiskCheckEnable: false,
	}

	assert.True(t, settings.HiddenSecConfigCheckEnable)
	assert.False(t, settings.CloudKillCheckEnable)
	assert.True(t, settings.VbVersionCheckEnable)
	assert.False(t, settings.PerformanceRiskCheckEnable)
}

func TestHealthCheckSettings_ZeroValues(t *testing.T) {
	settings := &HealthCheckSettings{}

	assert.False(t, settings.HiddenSecConfigCheckEnable)
	assert.False(t, settings.CloudKillCheckEnable)
	assert.False(t, settings.VbVersionCheckEnable)
	assert.False(t, settings.PerformanceRiskCheckEnable)
}

func TestHealthCheckSettings_AllEnabled(t *testing.T) {
	settings := &HealthCheckSettings{
		HiddenSecConfigCheckEnable: true,
		CloudKillCheckEnable:       true,
		VbVersionCheckEnable:       true,
		PerformanceRiskCheckEnable: true,
	}

	assert.True(t, settings.HiddenSecConfigCheckEnable)
	assert.True(t, settings.CloudKillCheckEnable)
	assert.True(t, settings.VbVersionCheckEnable)
	assert.True(t, settings.PerformanceRiskCheckEnable)
}

func TestSettingsUtils_LastSettings(t *testing.T) {
	settingsUtils := &SettingsUtils{
		LastSettings: &HealthCheckSettings{
			HiddenSecConfigCheckEnable: true,
			CloudKillCheckEnable:       false,
			VbVersionCheckEnable:       true,
			PerformanceRiskCheckEnable: false,
		},
	}

	assert.NotNil(t, settingsUtils.LastSettings)
	assert.True(t, settingsUtils.LastSettings.HiddenSecConfigCheckEnable)
	assert.False(t, settingsUtils.LastSettings.CloudKillCheckEnable)
	assert.True(t, settingsUtils.LastSettings.VbVersionCheckEnable)
	assert.False(t, settingsUtils.LastSettings.PerformanceRiskCheckEnable)
}
