package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/zeus"
)

func TestSetAssetId(t *testing.T) {
	// Reset global variable
	_assetId = nil

	testAssetId := &zeus.AssetId{
		Id:  123,
		Oid: 456,
	}

	setAssetId(testAssetId)

	assert.Equal(t, testAssetId, _assetId)
}

func TestAssetId_AlreadySet(t *testing.T) {
	// Set up a pre-existing asset ID
	existingAssetId := &zeus.AssetId{
		Id:  999,
		Oid: 888,
	}
	_assetId = existingAssetId

	result, err := AssetId()

	assert.NoError(t, err)
	assert.Equal(t, existingAssetId, result)

	// Reset for other tests
	_assetId = nil
}
