package impl 

import (
	//"fmt"
	"context"
	"google.golang.org/grpc/codes"
	status "google.golang.org/genproto/googleapis/rpc/status"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	empty "github.com/golang/protobuf/ptypes/empty"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
)

type  HealthCheckImpl struct {
	jm 		JobManagerInterface 
	pUtils  PolicyUtilsInterface
}	

func NewHealthCheckImpl(sp server.ServiceProvider) *HealthCheckImpl {
	jm := NewJobManager(sp)
	return &HealthCheckImpl{
		jm: jm,
		pUtils: GetPolicyUtils(),
	}
}

func (hc *HealthCheckImpl) GetAntivirusHealthCheckResult(ctx context.Context, 
	request *av.GetAntiVirusHealthCheckResult_Request) (*av.GetAntiVirusHealthCheckResult_Response, error) {
	
	gflog.Debugf("GetAntivirusHealthCheckResult in req=%v", request)

	pkeys := make(map[int64]string)
	
	p, err := hc.pUtils.GetAntiVirusPoliciesPartiallyWithStaffid(ctx, 20)
	if err != nil {
		gflog.Errorf("GetAntiVirusPoliciesPartiallyWithStaffid failed err=%v", err)
		return nil, err
	}

	for _, v := range p {
		pkeys[v.Key.Id] = v.PolicyName
	}

	resps := []*av.AntiVirusHealthCheckResult_Response{}

	//获取一下对应的result
	keys := request.GetKeys() 
	for i := 0; i < len(keys); i++ {
		key := keys[i]
		offset := int(key.GetOffset())
		limit := int(key.GetLimit())
		checkID := key.GetCheckId()

		result, stat := hc.jm.GetResult(checkID)
		if stat.Code != 0 {
			r := &av.AntiVirusHealthCheckResult_Response{
				Result: result,
				Status: stat,
			}
			resps = append(resps, r)
			continue
		}
		
		//病毒库简单处理
		if checkID == av.HealthCheckItem_VIRUSLIB_VERSION || len(result.Policies) == 0 { 
			result.Total = int64(len(result.VirusLibs))
			r := &av.AntiVirusHealthCheckResult_Response{
				Result: result,
				Status: stat,
			}
			resps = append(resps, r)
			continue
		} 
		
		//筛选处理policy
		allowedPolicies := []*av.PolicyCheckResult{}
		for _, v := range result.Policies {
			if _, ok := pkeys[v.Key.Id]; ok {
				allowedPolicies = append(allowedPolicies, v)
			}
		} 
		gflog.Debugf("allowedPolicies is %v", allowedPolicies)
		
		//都没有权限，结束
		if len(allowedPolicies) < 1 || offset > len(allowedPolicies) {
			stat = &status.Status{Code: int32(codes.FailedPrecondition)}
			result.Policies = []*av.PolicyCheckResult{}
		} else {
			if offset + limit > len(allowedPolicies) {
				result.Policies = allowedPolicies[offset:]
			} else {
				result.Policies = allowedPolicies[offset:offset+limit]
			}
		}
		result.Total = int64(len(allowedPolicies))
		

		r := &av.AntiVirusHealthCheckResult_Response{
			Result: result,
			Status: stat,
		}
		resps = append(resps, r)
	}

	//返回
	gflog.Debugf("GetAntiVirusPoliciesPartially out resp=%v", resps)
	return &av.GetAntiVirusHealthCheckResult_Response{
		Responses: resps, 
	}, nil

}

func (hc *HealthCheckImpl) StartAntivirusHealthCheck(ctx context.Context, 
	request *av.StartAntiVirusHealthCheck_Request) (*empty.Empty, error) {
	//通知jobManager
	hc.jm.QuickCheck(request.GetCheckId())
	return &empty.Empty{}, nil
}








