package main

import (
	"context"
	//gen_type "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av/gen_type"
	gflog "git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	"git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/client"
	"git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/zeus"
	//"google.golang.org/grpc/metadata"
	"math/rand"
	"time"

	"git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"

	fclient "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/av_v1/client"
	"git-biz.qianxin-inc.cn/infra-components/data-access-framework/golang/data-access-go.git/da"
	//"google.golang.org/genproto/protobuf/field_mask"
	api "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/av_v1"
)

func main() {
	defer da.Init()()
//	CreateAntiRansom()

    BatchGetClientAntiransom()
}

func CreateAntiRansom() {
	c, err := fclient.NewAvV1ThinClient()
	if err != nil {
		gflog.Errorf("NewAvV1ThinClient failed %v", err)
		return
	}

	ids := []string{
		"6336228-feceb5d287efaa05efd3b2a98ced348e",
		"5202795-dac8adec2e2d5b88649538ededbf8498",
		"2185674-3b3ba66ef39ec271d382b14cae84314a",
		"0215809-f9c1362d3524a43462fbe0495572ff59",
		"6520795-6a5bd2a4bc7894719e52fe4bf1ede31c",
		"6368684-63747e0df68ffb4079ec7afd85ed2900",
		"4334908-16ae69bc9a206f341e11bdae5012c826",
		// "3020078-f79912f6d6c1c72ce974274095554d3c",
	}

	for i := 0; i < len(ids) ; i ++ {

		id := &client.ClientId{
			Id: ids[i],
			AssetId: &zeus.AssetId{
				Oid: 2715651939760080161,
				Id: 2874631888146793800,
			},
		}

		request := &av.PatchAntiRansomStatistics_Request{
			Key: id,
			UpdateTime: time.Now().Unix(),
			ProtectionStatus: rand.Int31n(100),
			DetectionTimes: rand.Int63n(1224),
			ThreatEvents:  rand.Int63n(1314), 
			ProtectedTimes: rand.Int63n(876),
			RecoveredFiles: rand.Int63n(26352),
		}

		_, err := c.PatchAntiransomStatistics(context.TODO(), request) 
		if err != nil {
			gflog.Errorf("PatchAntiRansomStatistics failed err=%v \n", err)
		}
	}
}

func BatchGetClientAntiransom() {
	c, err := fclient.NewAvV1ThinClient()
	if err != nil {
		gflog.Errorf("NewAvV1ThinClient failed %v", err)
		return
	}

	request := &api.BatchGetClientAntiransom_Request{
		Keys: []*api.GetClientAntiransom_Request{
			{
				Key: &client.ClientId{
					Id: "6336228-feceb5d287efaa05efd3b2a98ced348e",
					AssetId: &zeus.AssetId{
						Oid: 2715651939760080161,
						Id: 2874631888146793800,
					},
				},
			},
		},
	}

	_, err = c.BatchGetClientAntiransom(context.TODO(), request) 
	if err != nil {
		gflog.Errorf("PatchAntiRansomStatistics failed err=%v \n", err)
	}

}
