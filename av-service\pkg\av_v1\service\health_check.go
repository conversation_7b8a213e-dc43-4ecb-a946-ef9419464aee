package service

import (
	"context"
	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	empty "github.com/golang/protobuf/ptypes/empty"
)

type HealthCheckService interface {

	GetAntivirusHealthCheckResult(context.Context, 
		*av.GetAntiVirusHealthCheckResult_Request) (*av.GetAntiVirusHealthCheckResult_Response, error)
	StartAntivirusHealthCheck(context.Context, 
		*av.StartAntiVirusHealthCheck_Request) (*empty.Empty, error)
}

type HealthCheck struct {
	impl  HealthCheckService
}


func NewHealthCheck(srv HealthCheckService) *HealthCheck {
	return &HealthCheck{
		impl: srv,
	}
}

func (h *HealthCheck) GetAntivirusHealthCheckResult(ctx context.Context, 
	request *av.GetAntiVirusHealthCheckResult_Request) (*av.GetAntiVirusHealthCheckResult_Response, error) {
	return h.impl.GetAntivirusHealthCheckResult(ctx, request)

}

func (h *HealthCheck) StartAntivirusHealthCheck(ctx context.Context, 
	request *av.StartAntiVirusHealthCheck_Request) (*empty.Empty, error) {
	return h.impl.StartAntivirusHealthCheck(ctx, request)
}



