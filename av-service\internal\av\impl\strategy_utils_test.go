package impl

import (
	"testing"

	"github.com/stretchr/testify/assert"

	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	jarvis "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/jarvis"
)

func TestFilterPolicyByItemName_EmptyPolicies(t *testing.T) {
	// Test with empty policies slice
	policies := []*av.PolicyCheckResult{}
	result := FilterPolicyByItemName(policies, "test_item")
	
	assert.Empty(t, result)
}

func TestFilterPolicyByItemName_NilPolicies(t *testing.T) {
	// Test with nil policies slice
	result := FilterPolicyByItemName(nil, "test_item")
	
	assert.Empty(t, result)
}

func TestFilterPolicyByItemName_NoMatchingItems(t *testing.T) {
	// Test with policies that don't contain the target item
	policies := []*av.PolicyCheckResult{
		{
			Key: &jarvis.PolicyId{Id: 1},
			PolicyName: "Test Policy 1",
			Objects: []*jarvis.MapObjectKeyObjectValueEntry{
				{
					Key: &jarvis.ObjectKey{
						ItemName: "other_item",
					},
					Value: &jarvis.ObjectValue{
						Value: &jarvis.ObjectValue_StringScalar{
							StringScalar: &jarvis.String_Scalar{
								Value: "test_value",
							},
						},
					},
				},
			},
		},
	}
	
	result := FilterPolicyByItemName(policies, "target_item")
	assert.Empty(t, result)
}

func TestFilterPolicyByItemName_WithMatchingItems(t *testing.T) {
	// Test with policies that contain the target item
	policies := []*av.PolicyCheckResult{
		{
			Key: &jarvis.PolicyId{Id: 1},
			PolicyName: "Test Policy 1",
			Objects: []*jarvis.MapObjectKeyObjectValueEntry{
				{
					Key: &jarvis.ObjectKey{
						ItemName: "target_item",
					},
					Value: &jarvis.ObjectValue{
						Value: &jarvis.ObjectValue_StringScalar{
							StringScalar: &jarvis.String_Scalar{
								Value: "test_value",
							},
						},
					},
				},
				{
					Key: &jarvis.ObjectKey{
						ItemName: "other_item",
					},
					Value: &jarvis.ObjectValue{
						Value: &jarvis.ObjectValue_StringScalar{
							StringScalar: &jarvis.String_Scalar{
								Value: "other_value",
							},
						},
					},
				},
			},
		},
		{
			Key: &jarvis.PolicyId{Id: 2},
			PolicyName: "Test Policy 2",
			Objects: []*jarvis.MapObjectKeyObjectValueEntry{
				{
					Key: &jarvis.ObjectKey{
						ItemName: "different_item",
					},
					Value: &jarvis.ObjectValue{
						Value: &jarvis.ObjectValue_StringScalar{
							StringScalar: &jarvis.String_Scalar{
								Value: "different_value",
							},
						},
					},
				},
			},
		},
	}
	
	result := FilterPolicyByItemName(policies, "target_item")
	
	assert.Len(t, result, 1)
	assert.Equal(t, int64(1), result[0].Key.Id)
	assert.Equal(t, "Test Policy 1", result[0].PolicyName)
	assert.Len(t, result[0].Objects, 1) // Should only contain the matching object
	assert.Equal(t, "target_item", result[0].Objects[0].Key.ItemName)
}

func TestFilterPolicyByItemName_MultipleMatchingPolicies(t *testing.T) {
	// Test with multiple policies that contain the target item
	policies := []*av.PolicyCheckResult{
		{
			Key: &jarvis.PolicyId{Id: 1},
			PolicyName: "Test Policy 1",
			Objects: []*jarvis.MapObjectKeyObjectValueEntry{
				{
					Key: &jarvis.ObjectKey{
						ItemName: "target_item",
					},
					Value: &jarvis.ObjectValue{
						Value: &jarvis.ObjectValue_StringScalar{
							StringScalar: &jarvis.String_Scalar{
								Value: "value1",
							},
						},
					},
				},
			},
		},
		{
			Key: &jarvis.PolicyId{Id: 2},
			PolicyName: "Test Policy 2",
			Objects: []*jarvis.MapObjectKeyObjectValueEntry{
				{
					Key: &jarvis.ObjectKey{
						ItemName: "target_item",
					},
					Value: &jarvis.ObjectValue{
						Value: &jarvis.ObjectValue_StringScalar{
							StringScalar: &jarvis.String_Scalar{
								Value: "value2",
							},
						},
					},
				},
			},
		},
	}
	
	result := FilterPolicyByItemName(policies, "target_item")
	
	assert.Len(t, result, 2)
	assert.Equal(t, int64(1), result[0].Key.Id)
	assert.Equal(t, int64(2), result[1].Key.Id)
}

func TestFilterPolicyByItemName_PolicyWithNilObjects(t *testing.T) {
	// Test with policy that has nil Objects
	policies := []*av.PolicyCheckResult{
		{
			Key: &jarvis.PolicyId{Id: 1},
			PolicyName: "Test Policy 1",
			Objects: nil,
		},
	}
	
	result := FilterPolicyByItemName(policies, "target_item")
	assert.Empty(t, result)
}

func TestFilterPolicyByItemName_PolicyWithEmptyObjects(t *testing.T) {
	// Test with policy that has empty Objects slice
	policies := []*av.PolicyCheckResult{
		{
			Key: &jarvis.PolicyId{Id: 1},
			PolicyName: "Test Policy 1",
			Objects: []*jarvis.MapObjectKeyObjectValueEntry{},
		},
	}
	
	result := FilterPolicyByItemName(policies, "target_item")
	assert.Empty(t, result)
}

func TestFilterPolicyByItemName_ObjectWithNilKey(t *testing.T) {
	// Test with object that has nil Key
	policies := []*av.PolicyCheckResult{
		{
			Key: &jarvis.PolicyId{Id: 1},
			PolicyName: "Test Policy 1",
			Objects: []*jarvis.MapObjectKeyObjectValueEntry{
				{
					Key: nil,
					Value: &jarvis.ObjectValue{
						Value: &jarvis.ObjectValue_StringScalar{
							StringScalar: &jarvis.String_Scalar{
								Value: "test_value",
							},
						},
					},
				},
			},
		},
	}
	
	result := FilterPolicyByItemName(policies, "target_item")
	assert.Empty(t, result)
}

func TestFilterPolicyByItemName_EmptyItemName(t *testing.T) {
	// Test with empty target item name
	policies := []*av.PolicyCheckResult{
		{
			Key: &jarvis.PolicyId{Id: 1},
			PolicyName: "Test Policy 1",
			Objects: []*jarvis.MapObjectKeyObjectValueEntry{
				{
					Key: &jarvis.ObjectKey{
						ItemName: "",
					},
					Value: &jarvis.ObjectValue{
						Value: &jarvis.ObjectValue_StringScalar{
							StringScalar: &jarvis.String_Scalar{
								Value: "test_value",
							},
						},
					},
				},
			},
		},
	}
	
	result := FilterPolicyByItemName(policies, "")
	
	assert.Len(t, result, 1)
	assert.Equal(t, "", result[0].Objects[0].Key.ItemName)
}

func TestFilterPolicyByItemName_PreservePolicyMetadata(t *testing.T) {
	// Test that policy metadata is preserved in the result
	policies := []*av.PolicyCheckResult{
		{
			Key: &jarvis.PolicyId{Id: 123},
			PolicyName: "Important Policy",
			IsCascade: true,
			Objects: []*jarvis.MapObjectKeyObjectValueEntry{
				{
					Key: &jarvis.ObjectKey{
						ItemName: "target_item",
					},
					Value: &jarvis.ObjectValue{
						Value: &jarvis.ObjectValue_BoolScalar{
							BoolScalar: &jarvis.Bool_Scalar{
								Value: true,
							},
						},
					},
				},
			},
		},
	}
	
	result := FilterPolicyByItemName(policies, "target_item")
	
	assert.Len(t, result, 1)
	assert.Equal(t, int64(123), result[0].Key.Id)
	assert.Equal(t, "Important Policy", result[0].PolicyName)
	assert.True(t, result[0].IsCascade)
}
