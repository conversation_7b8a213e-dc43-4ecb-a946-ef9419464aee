package model

import (
	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
)


const (
	WINPC_VB = 1
	WINSRV_VB = 2
	MACOS_VB = 3
)

type HealthCheck struct {
	Id        		uint64 					`orm:"column(id);auto;pk"`       //主键，数据库自动生成
	CheckId 		int32 					`orm:"column(check_id)"`		 //检查项ID
	CheckTime 		int64 					`orm:"column(check_time);index"` //检查时间 单位s
	PolicyId 		int64     				`orm:"column(policy_id);index"`  //策略ID
	PolicyName 		string 					`orm:"column(policy_name)"`      //策略名称
	PolicyCascade 	bool    				`orm:"column(policy_cascade)"`   //是否为上级策略 
	PolicyItemKey 	string      			`orm:"column(policy_item_key)"`	 //具体策略项Key
	PolicyItemValue string 					`orm:"column(policy_item_value)"` //具体策略项Value
	VirusLibType 	int32 					`orm:"column(virus_lib_type)"`    //病毒库类型 （1-winPC 2-winSer 3-maxOS）
	VirusLibVersion string                  `orm:"column(virus_lib_version)"` //病毒库版本
	VirusLibPeriod 	uint32   				`orm:"column(virus_lib_period)"`  //病毒库过期时间范围
}

func (a *HealthCheck) TableName() string {
	return "health_check"
}

type HealthCheckModel interface {
	//按照checkid类别，做的删除和写入
	UpdateResult(checkId int32, result *av.HealthCheckResult) error
	GetResult(checkId int32, offset, limit int64) (*av.HealthCheckResult, error) 
	GetAllResult(checkId int32) (*av.HealthCheckResult, error)
}

