package model

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/client"
	"git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/zeus"
)

func TestAntiRansom_TableName(t *testing.T) {
	ar := &AntiRansom{}
	assert.Equal(t, "anti_ransom", ar.Table<PERSON>ame())
}

func TestAntiRansom_ConvertDTO(t *testing.T) {
	ar := &AntiRansom{
		ClientId:         "test-client-id",
		AssetId:          123,
		AssetOid:         456,
		UpdateTime:       1234567890,
		ProtectionStatus: 1,
		DetectionTimes:   10,
		ThreatEvents:     5,
		ProtectedTimes:   8,
		RecoveredFiles:   3,
	}

	dto := ar.ConvertDTO()

	assert.NotNil(t, dto)
	assert.Equal(t, "test-client-id", dto.ClientId.GetId())
	assert.Equal(t, int64(123), dto.ClientId.GetAssetId().GetId())
	assert.Equal(t, int64(456), dto.ClientId.GetAssetId().GetOid())
	assert.Equal(t, int64(1234567890), dto.UpdateTime)
	assert.Equal(t, int32(1), dto.ProtectionStatus)
	assert.Equal(t, int64(10), dto.DetectionTimes)
	assert.Equal(t, int64(5), dto.ThreatEvents)
	assert.Equal(t, int64(8), dto.ProtectedTimes)
	assert.Equal(t, int64(3), dto.RecoveredFiles)
}

func TestAntiRansomDTO_ConvertOrm(t *testing.T) {
	dto := &AntiRansomDTO{
		ClientId: &client.ClientId{
			Id: "test-client-id",
			AssetId: &zeus.AssetId{
				Id:  123,
				Oid: 456,
			},
		},
		UpdateTime:       1234567890,
		ProtectionStatus: 1,
		DetectionTimes:   10,
		ThreatEvents:     5,
		ProtectedTimes:   8,
		RecoveredFiles:   3,
	}

	orm := dto.ConvertOrm()

	assert.NotNil(t, orm)
	assert.Equal(t, "test-client-id", orm.ClientId)
	assert.Equal(t, int64(123), orm.AssetId)
	assert.Equal(t, int64(456), orm.AssetOid)
	assert.Equal(t, int64(1234567890), orm.UpdateTime)
	assert.Equal(t, int32(1), orm.ProtectionStatus)
	assert.Equal(t, int64(10), orm.DetectionTimes)
	assert.Equal(t, int64(5), orm.ThreatEvents)
	assert.Equal(t, int64(8), orm.ProtectedTimes)
	assert.Equal(t, int64(3), orm.RecoveredFiles)
}

func TestAntiRansomDTO_ConvertOrm_NilClientId(t *testing.T) {
	dto := &AntiRansomDTO{
		ClientId:         nil,
		UpdateTime:       1234567890,
		ProtectionStatus: 1,
		DetectionTimes:   10,
		ThreatEvents:     5,
		ProtectedTimes:   8,
		RecoveredFiles:   3,
	}

	// This should not panic, but will result in empty values
	assert.NotPanics(t, func() {
		orm := dto.ConvertOrm()
		assert.NotNil(t, orm)
		assert.Equal(t, "", orm.ClientId)
		assert.Equal(t, int64(0), orm.AssetId)
		assert.Equal(t, int64(0), orm.AssetOid)
	})
}

func TestAntiRansomDTO_ConvertOrm_NilAssetId(t *testing.T) {
	dto := &AntiRansomDTO{
		ClientId: &client.ClientId{
			Id:      "test-client-id",
			AssetId: nil,
		},
		UpdateTime:       1234567890,
		ProtectionStatus: 1,
		DetectionTimes:   10,
		ThreatEvents:     5,
		ProtectedTimes:   8,
		RecoveredFiles:   3,
	}

	// This should not panic, but will result in zero asset values
	assert.NotPanics(t, func() {
		orm := dto.ConvertOrm()
		assert.NotNil(t, orm)
		assert.Equal(t, "test-client-id", orm.ClientId)
		assert.Equal(t, int64(0), orm.AssetId)
		assert.Equal(t, int64(0), orm.AssetOid)
	})
}

func TestAntiRansom_ConvertDTO_RoundTrip(t *testing.T) {
	// Test round-trip conversion: AntiRansom -> DTO -> AntiRansom
	original := &AntiRansom{
		ClientId:         "test-client-id",
		AssetId:          123,
		AssetOid:         456,
		UpdateTime:       1234567890,
		ProtectionStatus: 1,
		DetectionTimes:   10,
		ThreatEvents:     5,
		ProtectedTimes:   8,
		RecoveredFiles:   3,
	}

	dto := original.ConvertDTO()
	converted := dto.ConvertOrm()

	assert.Equal(t, original.ClientId, converted.ClientId)
	assert.Equal(t, original.AssetId, converted.AssetId)
	assert.Equal(t, original.AssetOid, converted.AssetOid)
	assert.Equal(t, original.UpdateTime, converted.UpdateTime)
	assert.Equal(t, original.ProtectionStatus, converted.ProtectionStatus)
	assert.Equal(t, original.DetectionTimes, converted.DetectionTimes)
	assert.Equal(t, original.ThreatEvents, converted.ThreatEvents)
	assert.Equal(t, original.ProtectedTimes, converted.ProtectedTimes)
	assert.Equal(t, original.RecoveredFiles, converted.RecoveredFiles)
}
