{"database": "anti_virus", "tables": [{"name": "anti_ransom", "fields": [{"name": "clientid", "type": "<PERSON><PERSON><PERSON>", "size": 255, "is_null": false, "is_json": false}, {"name": "assetid", "type": "int64", "size": 0, "is_null": false, "is_json": false}, {"name": "assetoid", "type": "int64", "size": 0, "is_null": false, "is_json": false}, {"name": "update_time", "type": "int64", "size": 0, "is_null": false, "is_json": false}, {"name": "protection_status", "type": "int32", "size": 0, "is_null": false, "is_json": false}, {"name": "detection_times", "type": "int64", "size": 0, "is_null": false, "is_json": false}, {"name": "threat_events", "type": "int64", "size": 0, "is_null": false, "is_json": false}, {"name": "protected_times", "type": "int64", "size": 0, "is_null": false, "is_json": false}, {"name": "recovered_files", "type": "int64", "size": 0, "is_null": false, "is_json": false}], "primary_key": {"field": "clientid", "auto": false}, "indexes": [["update_time"]], "unique_indexes": []}], "version": "v1.0.0", "alias": "av"}