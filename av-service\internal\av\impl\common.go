package impl 

import (
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
	dao "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/dao"
	model "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/model"
)

func CreateAntiRansomModel(sp server.ServiceProvider) model.AntiRansomModel {
	return dao.NewAntiRansomDao(sp)
}


func CreateHealthCheckModel(checkId int32,sp server.ServiceProvider) model.HealthCheckModel {
	return dao.NewHealthCheckDao(checkId, sp)
}