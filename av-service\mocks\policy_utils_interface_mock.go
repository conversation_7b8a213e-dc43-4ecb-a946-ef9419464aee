// Code generated by MockGen. DO NOT EDIT.
// Source: ./internal/av/impl/strategy_utils.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	jarvis "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/jarvis"
	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	gomock "github.com/golang/mock/gomock"
)

// MockPolicyUtilsInterface is a mock of PolicyUtilsInterface interface.
type MockPolicyUtilsInterface struct {
	ctrl     *gomock.Controller
	recorder *MockPolicyUtilsInterfaceMockRecorder
}

// MockPolicyUtilsInterfaceMockRecorder is the mock recorder for MockPolicyUtilsInterface.
type MockPolicyUtilsInterfaceMockRecorder struct {
	mock *MockPolicyUtilsInterface
}

// NewMockPolicyUtilsInterface creates a new mock instance.
func NewMockPolicyUtilsInterface(ctrl *gomock.Controller) *MockPolicyUtilsInterface {
	mock := &MockPolicyUtilsInterface{ctrl: ctrl}
	mock.recorder = &MockPolicyUtilsInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPolicyUtilsInterface) EXPECT() *MockPolicyUtilsInterfaceMockRecorder {
	return m.recorder
}

// CountAntiVirusPolicies mocks base method.
func (m *MockPolicyUtilsInterface) CountAntiVirusPolicies(ctx context.Context) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountAntiVirusPolicies", ctx)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountAntiVirusPolicies indicates an expected call of CountAntiVirusPolicies.
func (mr *MockPolicyUtilsInterfaceMockRecorder) CountAntiVirusPolicies(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountAntiVirusPolicies", reflect.TypeOf((*MockPolicyUtilsInterface)(nil).CountAntiVirusPolicies), ctx)
}

// GetAntiVirusPoliciesPartially mocks base method.
func (m *MockPolicyUtilsInterface) GetAntiVirusPoliciesPartially(ctx context.Context, limit int64) ([]*av.PolicyCheckResult, []*jarvis.PolicyMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAntiVirusPoliciesPartially", ctx, limit)
	ret0, _ := ret[0].([]*av.PolicyCheckResult)
	ret1, _ := ret[1].([]*jarvis.PolicyMeta)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAntiVirusPoliciesPartially indicates an expected call of GetAntiVirusPoliciesPartially.
func (mr *MockPolicyUtilsInterfaceMockRecorder) GetAntiVirusPoliciesPartially(ctx, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAntiVirusPoliciesPartially", reflect.TypeOf((*MockPolicyUtilsInterface)(nil).GetAntiVirusPoliciesPartially), ctx, limit)
}

// GetAntiVirusPoliciesPartiallyWithStaffid mocks base method.
func (m *MockPolicyUtilsInterface) GetAntiVirusPoliciesPartiallyWithStaffid(ctx context.Context, limit int64) ([]*av.PolicyCheckResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAntiVirusPoliciesPartiallyWithStaffid", ctx, limit)
	ret0, _ := ret[0].([]*av.PolicyCheckResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAntiVirusPoliciesPartiallyWithStaffid indicates an expected call of GetAntiVirusPoliciesPartiallyWithStaffid.
func (mr *MockPolicyUtilsInterfaceMockRecorder) GetAntiVirusPoliciesPartiallyWithStaffid(ctx, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAntiVirusPoliciesPartiallyWithStaffid", reflect.TypeOf((*MockPolicyUtilsInterface)(nil).GetAntiVirusPoliciesPartiallyWithStaffid), ctx, limit)
}
