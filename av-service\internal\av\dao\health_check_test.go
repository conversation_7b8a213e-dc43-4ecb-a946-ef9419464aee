package dao

import (
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	timestamp "github.com/golang/protobuf/ptypes/timestamp"

	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
	model "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/model"
	jarvis "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/jarvis"
)

func TestNewHealthCheckDao(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockSP := NewMockServiceProvider(ctrl)
	mockSP.EXPECT().DefaultDB().Return(nil).AnyTimes()

	checkId := int32(1)
	dao := NewHealthCheckDao(checkId, mockSP)

	assert.NotNil(t, dao)
	assert.Equal(t, checkId, dao.CheckId)
	assert.Equal(t, mockSP, dao.Provider)
}

func TestHealthCheckDao_GetDb(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockSP := NewMockServiceProvider(ctrl)
	mockSP.EXPECT().DefaultDB().Return(nil).AnyTimes()

	dao := NewHealthCheckDao(1, mockSP)
	
	// This should not panic
	assert.NotPanics(t, func() {
		db := dao.GetDb()
		assert.NotNil(t, db)
	})
}

func TestViruslibVersionCheckResult2Model(t *testing.T) {
	now := time.Now()
	result := &av.HealthCheckResult{
		CheckTime: &timestamp.Timestamp{
			Seconds: now.Unix(),
		},
		VirusLibs: []*av.VirusLibCheckResult{
			{
				VbType:  "WINPC_VB",
				Version: "1.0.0",
				Days:    30,
			},
			{
				VbType:  "WINSRV_VB", 
				Version: "2.0.0",
				Days:    60,
			},
		},
	}

	models := ViruslibVersionCheckResult2Model(result)

	assert.Len(t, models, 2)
	
	// Check first model
	assert.Equal(t, int32(av.HealthCheckItem_VIRUSLIB_VERSION), models[0].CheckId)
	assert.Equal(t, now.Unix(), models[0].CheckTime)
	assert.Equal(t, "1.0.0", models[0].VirusLibVersion)
	assert.Equal(t, uint32(30), models[0].VirusLibPeriod)
	
	// Check second model
	assert.Equal(t, int32(av.HealthCheckItem_VIRUSLIB_VERSION), models[1].CheckId)
	assert.Equal(t, now.Unix(), models[1].CheckTime)
	assert.Equal(t, "2.0.0", models[1].VirusLibVersion)
	assert.Equal(t, uint32(60), models[1].VirusLibPeriod)
}

func TestViruslibVersionCheckResult2Model_NilCheckTime(t *testing.T) {
	result := &av.HealthCheckResult{
		CheckTime: nil,
		VirusLibs: []*av.VirusLibCheckResult{
			{
				VbType:  "WINPC_VB",
				Version: "1.0.0",
				Days:    30,
			},
		},
	}

	models := ViruslibVersionCheckResult2Model(result)

	assert.Len(t, models, 1)
	// Should use current time when CheckTime is nil
	assert.True(t, models[0].CheckTime > 0)
}

func TestViruslibVersionCheckResult2Model_EmptyVirusLibs(t *testing.T) {
	result := &av.HealthCheckResult{
		CheckTime: &timestamp.Timestamp{
			Seconds: time.Now().Unix(),
		},
		VirusLibs: []*av.VirusLibCheckResult{},
	}

	models := ViruslibVersionCheckResult2Model(result)

	assert.Len(t, models, 0)
}

func TestSecStrategyCheckResult2Model_ValidPolicies(t *testing.T) {
	policies := []*av.PolicyCheckResult{
		{
			PolicyId:      123,
			PolicyName:    "Test Policy",
			PolicyCascade: true,
			PolicyItems: []*jarvis.MapObjectKeyObjectValueEntry{
				{
					Key: &jarvis.ObjectKey{
						ItemName: "test_key",
					},
				},
			},
		},
	}

	models := SecStrategyCheckResult2Model(policies)

	assert.Len(t, models, 1)
	assert.Equal(t, int32(av.HealthCheckItem_SEC_HIDDEN_DANGER), models[0].CheckId)
	assert.Equal(t, int64(123), models[0].PolicyId)
	assert.Equal(t, "Test Policy", models[0].PolicyName)
	assert.True(t, models[0].PolicyCascade)
	assert.Equal(t, "test_key", models[0].PolicyItemKey)
}

func TestSecStrategyCheckResult2Model_EmptyPolicies(t *testing.T) {
	policies := []*av.PolicyCheckResult{}

	models := SecStrategyCheckResult2Model(policies)

	assert.Len(t, models, 0)
}

func TestSecStrategyCheckResult2Model_NilPolicies(t *testing.T) {
	models := SecStrategyCheckResult2Model(nil)

	assert.Len(t, models, 0)
}

func TestPerformanceStrategyCheckResult2Model_ValidPolicies(t *testing.T) {
	policies := []*av.PolicyCheckResult{
		{
			PolicyId:      456,
			PolicyName:    "Performance Policy",
			PolicyCascade: false,
			PolicyItems: []*jarvis.MapObjectKeyObjectValueEntry{
				{
					Key: &jarvis.ObjectKey{
						ItemName: "performance_key",
					},
				},
			},
		},
	}

	models := PerformanceStrategyCheckResult2Model(policies)

	assert.Len(t, models, 1)
	assert.Equal(t, int32(av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER), models[0].CheckId)
	assert.Equal(t, int64(456), models[0].PolicyId)
	assert.Equal(t, "Performance Policy", models[0].PolicyName)
	assert.False(t, models[0].PolicyCascade)
	assert.Equal(t, "performance_key", models[0].PolicyItemKey)
}

func TestPerformanceStrategyCheckResult2Model_EmptyPolicies(t *testing.T) {
	policies := []*av.PolicyCheckResult{}

	models := PerformanceStrategyCheckResult2Model(policies)

	assert.Len(t, models, 0)
}

func TestCloudKillCheckResult2Model_ValidResult(t *testing.T) {
	result := &av.HealthCheckResult{
		CheckTime: &timestamp.Timestamp{
			Seconds: time.Now().Unix(),
		},
		CloudKillAccessable: true,
	}

	models := CloudKillCheckResult2Model(result)

	assert.Len(t, models, 1)
	assert.Equal(t, int32(av.HealthCheckItem_ACCESS_ClOUD), models[0].CheckId)
	assert.True(t, models[0].CheckTime > 0)
}

func TestCloudKillCheckResult2Model_NilResult(t *testing.T) {
	models := CloudKillCheckResult2Model(nil)

	assert.Len(t, models, 0)
}
