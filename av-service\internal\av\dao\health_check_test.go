package dao

import (
	"testing"
	"time"

	timestamp "github.com/golang/protobuf/ptypes/timestamp"
	"github.com/stretchr/testify/assert"

	jarvis "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/jarvis"
	av "git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/api/av-api.git/generated-go/skylar/business/av"
)

// Note: NewHealthCheckDao requires a valid ServiceProvider with database connection
// Testing it would require setting up a full database environment
// For unit testing purposes, we focus on testing the conversion functions

func TestHealthCheckDao_Functions_Exist(t *testing.T) {
	// Test that the functions exist
	assert.NotNil(t, NewHealthCheckDao)
}

func TestViruslibVersionCheckResult2Model(t *testing.T) {
	now := time.Now()
	result := &av.HealthCheckResult{
		CheckTime: &timestamp.Timestamp{
			Seconds: now.Unix(),
		},
		VirusLibs: []*av.VirusLibCheckResult{
			{
				VbType:  "WINPC_VB",
				Version: "1.0.0",
				Days:    30,
			},
			{
				VbType:  "WINSRV_VB",
				Version: "2.0.0",
				Days:    60,
			},
		},
	}

	models := ViruslibVersionCheckResult2Model(result)

	assert.Len(t, models, 2)

	// Check first model
	assert.Equal(t, int32(av.HealthCheckItem_VIRUSLIB_VERSION), models[0].CheckId)
	assert.Equal(t, now.Unix(), models[0].CheckTime)
	assert.Equal(t, "1.0.0", models[0].VirusLibVersion)
	assert.Equal(t, uint32(30), models[0].VirusLibPeriod)

	// Check second model
	assert.Equal(t, int32(av.HealthCheckItem_VIRUSLIB_VERSION), models[1].CheckId)
	assert.Equal(t, now.Unix(), models[1].CheckTime)
	assert.Equal(t, "2.0.0", models[1].VirusLibVersion)
	assert.Equal(t, uint32(60), models[1].VirusLibPeriod)
}

func TestViruslibVersionCheckResult2Model_NilCheckTime(t *testing.T) {
	result := &av.HealthCheckResult{
		CheckTime: nil,
		VirusLibs: []*av.VirusLibCheckResult{
			{
				VbType:  "WINPC_VB",
				Version: "1.0.0",
				Days:    30,
			},
		},
	}

	models := ViruslibVersionCheckResult2Model(result)

	assert.Len(t, models, 1)
	// Should use current time when CheckTime is nil
	assert.True(t, models[0].CheckTime > 0)
}

func TestViruslibVersionCheckResult2Model_EmptyVirusLibs(t *testing.T) {
	result := &av.HealthCheckResult{
		CheckTime: &timestamp.Timestamp{
			Seconds: time.Now().Unix(),
		},
		VirusLibs: []*av.VirusLibCheckResult{},
	}

	models := ViruslibVersionCheckResult2Model(result)

	assert.Len(t, models, 0)
}

func TestSecHiddenDangerCheckResult2Model_ValidResult(t *testing.T) {
	result := &av.HealthCheckResult{
		Policies: []*av.PolicyCheckResult{
			{
				Key: &jarvis.PolicyId{
					Id: 123,
				},
				PolicyName: "Test Policy",
				IsCascade:  true,
				Objects: []*jarvis.MapObjectKeyObjectValueEntry{
					{
						Key: &jarvis.ObjectKey{
							ItemName: "test_key",
						},
						Value: &jarvis.ObjectValue{
							Value: &jarvis.ObjectValue_IntScalar{
								IntScalar: &jarvis.Int_Scalar{
									Value: 42,
								},
							},
						},
					},
				},
			},
		},
	}

	models := SecHiddenDangerCheckResult2Model(result)

	assert.Len(t, models, 1)
	assert.Equal(t, int64(123), models[0].PolicyId)
	assert.Equal(t, "Test Policy", models[0].PolicyName)
	assert.True(t, models[0].PolicyCascade)
	assert.Equal(t, "test_key", models[0].PolicyItemKey)
	assert.Equal(t, "42", models[0].PolicyItemValue)
}

func TestSecHiddenDangerCheckResult2Model_EmptyResult(t *testing.T) {
	result := &av.HealthCheckResult{
		Policies: []*av.PolicyCheckResult{},
	}

	models := SecHiddenDangerCheckResult2Model(result)

	assert.Len(t, models, 0)
}

func TestPerformanceHiddenDangerCheckResult2Model_ValidResult(t *testing.T) {
	result := &av.HealthCheckResult{
		CheckId: av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER,
		Policies: []*av.PolicyCheckResult{
			{
				Key: &jarvis.PolicyId{
					Id: 456,
				},
				PolicyName: "Performance Policy",
				IsCascade:  false,
				Objects: []*jarvis.MapObjectKeyObjectValueEntry{
					{
						Key: &jarvis.ObjectKey{
							ItemName: "performance_key",
						},
						Value: &jarvis.ObjectValue{
							Value: &jarvis.ObjectValue_BoolScalar{
								BoolScalar: &jarvis.Bool_Scalar{
									Value: true,
								},
							},
						},
					},
				},
			},
		},
	}

	models := PerformanceHiddenDangerCheckResult2Model(result)

	assert.Len(t, models, 1)
	assert.Equal(t, int32(av.HealthCheckItem_PERFORMANCE_HIDDEN_DANGER), models[0].CheckId)
	assert.Equal(t, int64(456), models[0].PolicyId)
	assert.Equal(t, "Performance Policy", models[0].PolicyName)
	assert.False(t, models[0].PolicyCascade)
	assert.Equal(t, "performance_key", models[0].PolicyItemKey)
	assert.Equal(t, "true", models[0].PolicyItemValue)
}

func TestPerformanceHiddenDangerCheckResult2Model_EmptyResult(t *testing.T) {
	result := &av.HealthCheckResult{
		Policies: []*av.PolicyCheckResult{},
	}

	models := PerformanceHiddenDangerCheckResult2Model(result)

	assert.Len(t, models, 0)
}

func TestAccessCloudCheckResult2Model_ValidResult(t *testing.T) {
	result := &av.HealthCheckResult{
		CheckId: av.HealthCheckItem_ACCESS_ClOUD,
		CheckTime: &timestamp.Timestamp{
			Seconds: time.Now().Unix(),
		},
		Policies: []*av.PolicyCheckResult{
			{
				Key: &jarvis.PolicyId{
					Id: 789,
				},
				PolicyName: "Cloud Access Policy",
				IsCascade:  true,
				Objects: []*jarvis.MapObjectKeyObjectValueEntry{
					{
						Key: &jarvis.ObjectKey{
							ItemName: "cloud_access_key",
						},
						Value: &jarvis.ObjectValue{
							Value: &jarvis.ObjectValue_StringScalar{
								StringScalar: &jarvis.String_Scalar{
									Value: "enabled",
								},
							},
						},
					},
				},
			},
		},
	}

	models := AccessCloudCheckResult2Model(result)

	assert.Len(t, models, 1)
	assert.Equal(t, int32(av.HealthCheckItem_ACCESS_ClOUD), models[0].CheckId)
	assert.Equal(t, int64(789), models[0].PolicyId)
	assert.Equal(t, "Cloud Access Policy", models[0].PolicyName)
	assert.True(t, models[0].PolicyCascade)
	assert.Equal(t, "cloud_access_key", models[0].PolicyItemKey)
	assert.Equal(t, "enabled", models[0].PolicyItemValue)
	assert.True(t, models[0].CheckTime > 0)
}

func TestAccessCloudCheckResult2Model_NilResult(t *testing.T) {
	models := AccessCloudCheckResult2Model(nil)

	assert.Len(t, models, 0)
}
