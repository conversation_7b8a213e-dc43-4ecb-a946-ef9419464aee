package main

import (
	"context"
	"fmt"
	"time"

	"git-biz.qianxin-inc.cn/zeus-platform/zeus-da-api.git/generated-go/zeus"
	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/log"
	pcli "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/upgrade_v1/client"
	upgradeApi "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/upgrade_v1"
	"git-biz.qianxin-inc.cn/tianqing/server/skylar-v10/service/av-service.git/internal/av/utils"
	"git-biz.qianxin-inc.cn/infra-components/data-access-framework/golang/data-access-go.git/da"
	key "git-biz.qianxin-inc.cn/skylar-ng/skylar-common/trantor-core.git/generated-go/trantor/core/upgrade/key"
)

const (
	limit       = 100
	offsetStep  = 20
	maxRetries  = 3
	retryDelay  = 2 * time.Second
)

func main() {
	defer da.Init()()

	cli, err := pcli.NewUpgradeV1ThinClient()
	if err != nil {
		log.Errorf("Failed to create UpgradeV1ThinClient: %v", err)
		return
	}

	assetID, err := utils.AssetId()
	if err != nil {
		log.Errorf("Failed to retrieve AssetId: %v", err)
		return
	}

	offset := int64(0)

	for {
		cnt, err := fetchProducts(cli, assetID, offset)
		if err != nil {
			log.Errorf("Fetch products failed with error: %v", err)
			break
		}
		if cnt == 0 {
			break
		}
		offset += offsetStep
	}
}

func fetchProducts(cli upgradeApi.UpgradeV1Client, assetID *zeus.AssetId, offset int64) (int, error) {
	
	for attempt := 1; attempt <= maxRetries; attempt++ {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		req := &upgradeApi.GetProductListPartially_Request{
			Key: &key.ProductId{
				Id:      "0",
				AssetId: assetID,
			},
			Offset: offset,
			Limit:  limit,
		}

		resp, err := cli.GetProductListPartially(ctx, req)
		if err != nil {
			log.Errorf("Attempt %d - GetProductListPartially failed: %v", attempt, err)
			if attempt == maxRetries {
				return -1, err
			}
			time.Sleep(retryDelay)
			continue
		}
		fmt.Printf("---------------------------\n")
		fmt.Printf("Offset: %d | Limit: %d | List Length: %d\n", offset, limit, len(resp.List))
		for i, product := range resp.List {
			fmt.Println(i, " : ",product.Id)
		}
		fmt.Printf("---------------------------\n")
		return len(resp.List), nil
	}
	return -1, fmt.Errorf("retries exhausted")
}