package impl

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git-biz.qianxin-inc.cn/infra-components/sdk/microservice-framework/go-framework.git/server"
)

// MockServiceProvider is a mock implementation of server.ServiceProvider
type MockServiceProvider struct {
	ctrl     *gomock.Controller
	recorder *MockServiceProviderMockRecorder
}

type MockServiceProviderMockRecorder struct {
	mock *MockServiceProvider
}

func NewMockServiceProvider(ctrl *gomock.Controller) *MockServiceProvider {
	mock := &MockServiceProvider{ctrl: ctrl}
	mock.recorder = &MockServiceProviderMockRecorder{mock}
	return mock
}

func (m *MockServiceProvider) EXPECT() *MockServiceProviderMockRecorder {
	return m.recorder
}

func (m *MockServiceProvider) DefaultDB() interface{} {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DefaultDB")
	ret0, _ := ret[0].(interface{})
	return ret0
}

func TestCreateAntiRansomModel(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockSP := NewMockServiceProvider(ctrl)
	mockSP.EXPECT().DefaultDB().Return(nil).AnyTimes()

	model := CreateAntiRansomModel(mockSP)

	assert.NotNil(t, model)
}

func TestCreateHealthCheckModel(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockSP := NewMockServiceProvider(ctrl)
	mockSP.EXPECT().DefaultDB().Return(nil).AnyTimes()

	checkId := int32(1)
	model := CreateHealthCheckModel(checkId, mockSP)

	assert.NotNil(t, model)
}

func TestCreateAntiRansomModel_NilServiceProvider(t *testing.T) {
	// This should not panic even with nil service provider
	assert.NotPanics(t, func() {
		model := CreateAntiRansomModel(nil)
		assert.NotNil(t, model)
	})
}

func TestCreateHealthCheckModel_NilServiceProvider(t *testing.T) {
	// This should not panic even with nil service provider
	assert.NotPanics(t, func() {
		checkId := int32(1)
		model := CreateHealthCheckModel(checkId, nil)
		assert.NotNil(t, model)
	})
}

func TestCreateHealthCheckModel_DifferentCheckIds(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockSP := NewMockServiceProvider(ctrl)
	mockSP.EXPECT().DefaultDB().Return(nil).AnyTimes()

	checkIds := []int32{1, 2, 3, 100, -1}

	for _, checkId := range checkIds {
		t.Run("CheckId_"+string(rune(checkId)), func(t *testing.T) {
			model := CreateHealthCheckModel(checkId, mockSP)
			assert.NotNil(t, model)
		})
	}
}
